package com.inossem.wms.bizdomain.demandplan.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.common.model.bizdomain.demandplan.dto.BizReceiptDemandPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.demandplan.entity.BizReceiptDemandPlanItem;
import com.inossem.wms.common.model.bizdomain.demandplan.po.BizReceiptDemandPlanSearchPO;
import com.inossem.wms.common.mybatisplus.WmsBaseMapper;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import org.apache.ibatis.annotations.Param;

/**
 * 需求计划明细表Mapper接口
 */
public interface BizReceiptDemandPlanItemMapper extends WmsBaseMapper<BizReceiptDemandPlanItem> {

    /**
     * 获取待整合的需求计划行项目
     *
     * @param page 分页对象
     * @param wrapper 查询条件
     * @return 分页结果
     */
    IPage<BizReceiptDemandPlanItemDTO> getUnMergeItems(
        IPage<BizReceiptDemandPlanItemDTO> page,
        @Param("ew") WmsQueryWrapper<BizReceiptDemandPlanSearchPO> wrapper
    );

}
