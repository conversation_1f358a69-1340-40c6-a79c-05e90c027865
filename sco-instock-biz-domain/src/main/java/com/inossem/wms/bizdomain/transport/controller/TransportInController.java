package com.inossem.wms.bizdomain.transport.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.transport.service.biz.TransportInService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportBinDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportInPreHeadVo;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportOutPreHeadVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调拨入库
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "调拨管理-调拨入库")
public class TransportInController {

    @Autowired
    private TransportInService transportInService;

    /**
     * 页面初始化
     *
     * @in po 查询条件
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/init")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> init(BizContext ctx) {
        transportInService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/getStock")
    public BaseResult<SingleResultVO<BizReceiptAssembleRuleDTO>>
        getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportInService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询出库单库存
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询出库单库存", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/getOutStock")
    public BaseResult<MultiResultVO<BizReceiptTransportBinDTO>>
        getOutStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportInService.getOutStock(ctx);
        MultiResultVO<BizReceiptTransportBinDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 调拨入库-前置单据调拨申请
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "调拨入库-前置单据调拨申请", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/mat-list")
    public BaseResult<MultiResultVO<BizReceiptTransportOutPreHeadVo>>
        getReferReceiptItemList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportInService.getReferReceiptItemList(ctx);
        MultiResultVO<BizReceiptTransportOutPreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 调拨入库-前置单据调拨出库
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "调拨入库-前置单据调拨出库", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/mat-list-out")
    public BaseResult<MultiResultVO<BizReceiptTransportInPreHeadVo>>
        getReferReceiptItemListByOut(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportInService.getReferReceiptItemListByOut(ctx);
        MultiResultVO<BizReceiptTransportInPreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/results")
    public BaseResult<PageObjectVO<BizReceiptTransportHeadDTO>> getPage(@RequestBody BizReceiptTransportHeadSearchPO po,
        BizContext ctx) {
        transportInService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 没有分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/list")
    public BaseResult<MultiResultVO<BizReceiptTransportHeadDTO>>
        getList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportInService.getList(ctx);
        MultiResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"调拨管理-调拨入库"})
    @GetMapping(value = "/transport/in/{id}")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportInService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/save")
    public BaseResult<String> save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportInService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/submit")
    public BaseResult<String> submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportInService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_CODE);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"调拨管理-调拨入库"})
    @PostMapping(value = "/transport/in/post")
    public BaseResult<String> post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportInService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"调拨管理-调拨入库"})
    @DeleteMapping("/transport/in/{id}")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        transportInService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转储管理"})
    @PostMapping("/transport/in/write-off")
    public BaseResult<String> writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportInService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

}