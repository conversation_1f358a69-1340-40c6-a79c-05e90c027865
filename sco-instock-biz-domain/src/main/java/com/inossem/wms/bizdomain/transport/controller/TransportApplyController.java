package com.inossem.wms.bizdomain.transport.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.transport.service.biz.TransportApplyService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportApplyHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调拨申请
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "调拨管理-调拨申请")
public class TransportApplyController {

    @Autowired
    private TransportApplyService transportApplyService;

    /**
     * 页面初始化
     *
     * @in po 查询条件
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/init")
    public BaseResult init(@RequestBody BizReceiptTransportApplyHeadDTO po, BizContext ctx) {
        transportApplyService.init(ctx);
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/getStock")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportApplyService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportApplyService.getPage(ctx);
        PageObjectVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 没有分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/list")
    public BaseResult getList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportApplyService.getList(ctx);
        MultiResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"调拨管理-调拨申请"})
    @GetMapping(value = "/transport/apply/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportApplyService.getInfo(ctx);
        BizResultVO<BizReceiptTransportApplyHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/save")
    public BaseResult save(@RequestBody BizReceiptTransportApplyHeadDTO po, BizContext ctx) {
        transportApplyService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"调拨管理-调拨申请"})
    @PostMapping(value = "/transport/apply/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportApplyHeadDTO po, BizContext ctx) {
        transportApplyService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"调拨管理-调拨申请"})
    @DeleteMapping("/transport/apply/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportApplyService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

}