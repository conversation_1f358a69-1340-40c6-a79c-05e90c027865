<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.bi.dao.BiPurchaseMapper">

    <select id="selectContractAmountSum" resultType="java.math.BigDecimal">
        select
            ifnull((select SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0)))
             from biz_receipt_contract_head h
             where receipt_type = 403
               and receipt_status = 90
               and is_delete = 0
               and YEAR(create_time) = #{year}), 0)

                +

            ifnull((select sum(ifnull(i.tax_price * i.qty, 0))
             from biz_receipt_contract_head h
                      join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
             where h.receipt_type = 402
               and h.receipt_status = 90
               and h.is_delete = 0
               and YEAR(h.create_time) = #{year}), 0)
    </select>

    <select id="selectContractDetailForAmountSum" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiContractAmountDetailVO">
        select
            h.id,
            h.currency,
            YEAR(h.create_time) as year,
            MONTH(h.create_time) as month,
            (h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0)
                * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))) as amount
        from biz_receipt_contract_head h
        where receipt_type = 403
          and receipt_status = 90
          and is_delete = 0
          and YEAR(create_time) = #{year}

        UNION ALL

        select
            h.id,
            h.currency,
            YEAR(h.create_time) as year,
            MONTH(h.create_time) as month,
            ifnull(i.tax_price * i.qty, 0) as amount
        from biz_receipt_contract_head h
                 join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
        where h.receipt_type = 402
          and h.receipt_status = 90
          and h.is_delete = 0
          and YEAR(h.create_time) = #{year}
    </select>

    <select id="getCompletedContractCount" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO">
        select year(create_time)                 year,
               month(create_time)                month,
               SUM(IF(receipt_type = 402, 1, 0)) count402,
               SUM(IF(receipt_type = 403, 1, 0)) count403,
               COUNT(1)                          count_total
        from biz_receipt_contract_head
        where receipt_type in (402, 403)
          and receipt_status = 90
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="getCompletedContractAmount402" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(h.create_time)                 year,
               month(h.create_time)                month,
               sum(ifnull(i.tax_price * i.qty, 0)) amount402
        from biz_receipt_contract_head h
                 join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
        where h.receipt_type = 403
          and h.receipt_status = 90
          and h.is_delete = 0
          AND year(h.create_time) = #{po.year}
          <if test="po.month != null">
              AND month(h.create_time) &lt;= #{po.month}
          </if>
        group by year(h.create_time),
                 month(h.create_time)
    </select>

    <select id="getCompletedContractAmount403" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO">
        select year(create_time)                                                                                                                  year,
               month(create_time)                                                                                                                 month,
               SUM((h.demand_qty * IFNULL((select no_tax_price from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))
                   * (1 + IFNULL((select tax_code_rate from biz_receipt_contract_item i where i.head_id = h.id and i.is_delete = 0 limit 1), 0))) amount403
        from biz_receipt_contract_head h
        where receipt_type = 403
          and receipt_status = 90
          and is_delete = 0
          AND year(create_time) = #{po.year}
          <if test="po.month != null">
              AND month(create_time) &lt;= #{po.month}
          </if>
        group by year(create_time),
                 month(create_time)
    </select>

    <select id="selectDemandPlanHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseDemandPlanVO">
        select id,
               receipt_code,
               create_time,
               create_user_id,
               handle_user_id,
               modify_time,
               demand_plan_type,
               demand_type
        from biz_receipt_demand_plan_head
        where is_delete = 0
          and receipt_type = 400
    </select>

    <select id="selectPurchaseHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchasePurchaseVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.demand_plan_type,
               h.purchase_type,
               h.bid_method,
               h.purchase_subject,
               h.annual_budget_id,
               h.budget_amount,
               h.create_time,
               h.create_user_id,
               h.modify_time,
               ph.id demand_head_id
        from biz_receipt_purchase_apply_head h
                 left join biz_receipt_purchase_apply_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on ph.id = i.pre_receipt_id and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (401, 204, 4013, 4014)
        group by h.id
    </select>

    <select id="selectContractHead"
            resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiPurchaseContractVO">
        select h.id,
               h.receipt_code,
               h.receipt_type,
               h.purchase_type,
               h.first_party,
               h.supplier_id,
               CASE WHEN h.receipt_type = 403 THEN i.no_tax_price ELSE 0 END                                            oil_price,
               CASE WHEN h.receipt_type = 403 THEN h.demand_qty * i.no_tax_price ELSE h.contract_amount_exclude_tax END amount,
               h.create_time,
               ah.id                                                                                                    purchase_head_id,
               ph.id                                                                                                    demand_head_id
        from biz_receipt_contract_head h
                 left join biz_receipt_contract_item i on i.head_id = h.id and i.is_delete = 0
                 left join biz_receipt_purchase_apply_head ah on (ah.id = i.pre_receipt_head_id or h.oil_purchase_code = ah.receipt_code) and ah.is_delete = 0
                 left join biz_receipt_purchase_apply_item ai on ai.head_id = ah.id and ai.is_delete = 0
                 left join biz_receipt_demand_plan_head ph on (ph.id = ai.pre_receipt_id or i.demand_plan_code = ph.receipt_code) and ph.is_delete = 0
        where h.is_delete = 0
          and h.receipt_type in (402, 403)
        group by h.id
    </select>

    <select id="selectBudgetClassifyList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbc.id,
               dbc.budget_classify_code          as budget_code,
               dbc.budget_classify_name          as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbc.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 left join sys_user su ON dbc.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
        group by dbc.id
    </select>

    <select id="selectBudgetSubjectList" resultType="com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO">
        SELECT dbs.id,
               dbs.budget_subject_code           as budget_code,
               dbs.budget_subject_name           as budget_name,
               sum(dab.budget_amount)            as budget_amount,
               sum(dab.existing_contract_amount) as contract_amount,
               su.user_code                      as create_user_code,
               su.user_name                      as create_user_name,
               dbs.create_time
        FROM dic_annual_budget dab
                 inner join dic_budget_classify dbc ON dbc.id = dab.budget_classify_id AND dbc.is_delete = 0
                 inner join dic_budget_subject dbs ON dbs.id = dab.budget_subject_id AND dbs.is_delete = 0
                 left join sys_user su ON dbs.create_user_id = su.id AND su.is_delete = 0
        where dab.is_delete = 0
          and dab.year = #{po.year}
          and dab.budget_classify_id = #{po.budgetClassifyId}
        group by dbs.id
    </select>

</mapper>
