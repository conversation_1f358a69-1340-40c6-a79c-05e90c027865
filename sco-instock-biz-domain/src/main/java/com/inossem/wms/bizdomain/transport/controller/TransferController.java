package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransferService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * 转性
 *
 */

@RestController
@Api(tags = "转储管理")
public class TransferController {

    @Autowired
    private TransferService transferService;

    /**
     * 移动类型列表
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transferService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 页面初始化
     *
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transferService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存 - 只选到物料
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/getStock")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transferService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 转性物料导入
     */
    @ApiOperation(value = "转性物料导入", notes = "转性物料导入", tags = {"转储管理-转性物料导入"})
    @PostMapping(path = "/transfer/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importMaterial(@RequestPart("file") MultipartFile file, @RequestPart("po") String po, BizContext ctx) {
        transferService.importMaterial(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo =ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表 - 分页", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transferService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"转储管理-物资转性"})
    @GetMapping(value = "/transfer/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transferService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transferService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }


    @ApiOperation(value = "提交", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transferService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transferService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"转储管理-物资转性"})
    @DeleteMapping("/transfer/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transferService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转储管理-物资转性"})
    @PostMapping("/transfer/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transferService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 获取WBS集合
     */
    @ApiOperation(value = "获取WBS集合", tags = {"转储管理-物资转性"})
    @PostMapping(value = "/transfer/wbs")
    public BaseResult getWbsList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transferService.getWbsList(ctx);
        MultiResultVO<ErpWbs> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}