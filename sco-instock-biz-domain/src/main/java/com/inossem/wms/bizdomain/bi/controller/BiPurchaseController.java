package com.inossem.wms.bizdomain.bi.controller;

import com.inossem.wms.bizdomain.bi.service.biz.BiPurchaseService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.bizdomain.bi.po.BiBudgetSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetClassifySubjectVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiBudgetSumVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractAmountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractCountVO;
import com.inossem.wms.common.model.bizdomain.bi.vo.BiCompletedContractSumVO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * BI-华信资源驾驶舱-采购类指标 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@RestController
@Api(tags = "BI-华信资源驾驶舱-采购类指标")
public class BiPurchaseController {

    @Autowired
    protected BiPurchaseService biPurchaseService;

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同数量/金额-总和", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-sum/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BiCompletedContractSumVO> getCompletedContractSum(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractSum(ctx);
        BiCompletedContractSumVO vo = ctx.getVoContextData();
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同数量", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-count/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiCompletedContractCountVO>> getCompletedContractCount(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractCount(ctx);
        List<BiCompletedContractCountVO> voList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(voList);
    }

    @ApiOperation(value = "BI-采购类指标-已完成采购任务的合同金额", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/completed-contract-amount/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiCompletedContractAmountVO>> getCompletedContractAmount(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getCompletedContractAmount(ctx);
        List<BiCompletedContractAmountVO> voList = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(voList);
    }

    @ApiOperation(value = "BI-采购类指标-采购年度预算金额表-导入", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(path = "/bi-purchase/budget-year/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importData(@RequestPart("file") MultipartFile file, BizContext ctx) {
        biPurchaseService.importBudgetYearData(ctx);
        return BaseResult.success();
    }

    @ApiOperation(value = "BI-采购类指标-采购年度预算金额表-导出", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(value = "/bi-purchase/budget-year/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> exportExcel(BizContext ctx) {
        biPurchaseService.exportBudgetYearExcel(ctx);
        return BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    @ApiOperation(value = "BI-采购类指标-预算达成率-总和", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(value = "/bi-purchase/budget-sum/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<BiBudgetSumVO> getBudgetSum(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getBudgetSum(ctx);
        BiBudgetSumVO vo = ctx.getVoContextData();
        return BaseResult.success(vo);
    }

    @ApiOperation(value = "BI-采购类指标-预算分类", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(value = "/bi-purchase/budget-classify/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiBudgetClassifySubjectVO>> getBudgetClassify(@RequestBody BiSearchPO po, BizContext ctx) {
        biPurchaseService.getBudgetClassify(ctx);
        List<BiBudgetClassifySubjectVO> voList = ctx.getVoContextData();
        return BaseResult.success(voList);
    }

    @ApiOperation(value = "BI-采购类指标-预算科目", tags = {"BI-华信资源驾驶舱-采购类指标"})
    @PostMapping(value = "/bi-purchase/budget-subject/result", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<List<BiBudgetClassifySubjectVO>> getBudgetSubject(@RequestBody BiBudgetSearchPO po, BizContext ctx) {
        biPurchaseService.getBudgetSubject(ctx);
        List<BiBudgetClassifySubjectVO> voList = ctx.getVoContextData();
        return BaseResult.success(voList);
    }

}
