package com.inossem.wms.bizdomain.settlement.service.component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizdomain.contract.service.component.ContractReceivingComponent;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractPaymentNodeDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractReceivingHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractSubItemDataWrap;
import com.inossem.wms.bizdomain.delivery.service.component.DeliveryNoticeComponent;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeHeadDataWrap;
import com.inossem.wms.bizdomain.delivery.service.datawrap.BizReceiptDeliveryNoticeItemDataWrap;
import com.inossem.wms.bizdomain.deliverywaybill.service.datawrap.BizReceiptDeliveryWaybillHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.component.InspectInputComponent;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputHeadDataWrap;
import com.inossem.wms.bizdomain.input.service.datawrap.BizReceiptInputItemDataWrap;
import com.inossem.wms.bizdomain.inspect.service.datawrap.BizReceiptInspectItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptCapitalPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptCapitalPlanItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanItemDataWrap;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSendType;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.contract.EnumContractCurrency;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.enums.contract.EnumContractPaymentNode;
import com.inossem.wms.common.enums.purchase.EnumPurchaseType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizbasis.entity.BizCommonReceiptRelation;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractPaymentNodeDTO;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractReceivingHeadDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractPaymentNode;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractReceivingHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractSubItem;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeHeadDTO;
import com.inossem.wms.common.model.bizdomain.delivery.dto.BizReceiptDeliveryNoticeItemDTO;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeHead;
import com.inossem.wms.common.model.bizdomain.delivery.entity.BizReceiptDeliveryNoticeItem;
import com.inossem.wms.common.model.bizdomain.deliverywaybill.entity.BizReceiptDeliveryWaybillHead;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputHeadDTO;
import com.inossem.wms.common.model.bizdomain.input.dto.BizReceiptInputItemDTO;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputHead;
import com.inossem.wms.common.model.bizdomain.input.entity.BizReceiptInputItem;
import com.inossem.wms.common.model.bizdomain.inspect.dto.BizReceiptInspectItemDTO;
import com.inossem.wms.common.model.bizdomain.inspect.entity.BizReceiptInspectItem;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptCapitalPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentPlanItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptCapitalPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptCapitalPlanItem;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanItem;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentPlanSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanPageVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.mat.info.dto.DicMaterialDTO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.mybatisplus.WmsQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Component
@Slf4j
public class PaymentPlanComponent {

    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;

    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;

    @Autowired
    private BizReceiptPaymentPlanItemDataWrap bizReceiptPaymentPlanItemDataWrap;

    @Autowired
    private ReceiptRelationService receiptRelationService;

    @Autowired
    private BizCommonService bizCommonService;

    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;

    @Autowired
    private BizReceiptCapitalPlanHeadDataWrap bizReceiptCapitalPlanHeadDataWrap;

    @Autowired
    private BizReceiptCapitalPlanItemDataWrap bizReceiptCapitalPlanItemDataWrap;

    @Autowired
    private DataFillService dataFillService;

    @Autowired
    private BizReceiptDeliveryNoticeHeadDataWrap bizReceiptDeliveryNoticeHeadDataWrap;

    @Autowired
    private BizReceiptDeliveryNoticeItemDataWrap bizReceiptDeliveryNoticeItemDataWrap;

    @Autowired
    private DeliveryNoticeComponent deliveryNoticeComponent;

    @Autowired
    private DictionaryService dictionaryService;

    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;

    @Autowired
    private BizReceiptContractPaymentNodeDataWrap bizReceiptContractPaymentNodeDataWrap;

    @Autowired
    private BizReceiptInputHeadDataWrap bizReceiptInputHeadDataWrap;

    @Autowired
    private InspectInputComponent inspectInputComponent;

    @Autowired
    private BizReceiptInspectItemDataWrap inspectItemDataWrap;

    @Autowired
    private BizReceiptContractSubItemDataWrap bizReceiptContractSubItemDataWrap;

    @Autowired
    private BizReceiptDeliveryWaybillHeadDataWrap bizReceiptDeliveryWaybillHeadDataWrap;

    @Autowired
    private BizReceiptContractReceivingHeadDataWrap bizReceiptContractReceivingHeadDataWrap;

    @Autowired
    private ContractReceivingComponent contractReceivingComponent;

    @Autowired
    private BizReceiptInputItemDataWrap bizReceiptInputItemDataWrap;

    @Autowired
    private BizReceiptInspectItemDataWrap bizReceiptInspectItemDataWrap;


    /**
     * 分页查询付款计划
     */
    public void getPageVo(BizContext ctx) {

        // 上下文入参
        BizReceiptPaymentPlanSearchPO po = ctx.getPoContextData();

        // 分页处理
        IPage<BizReceiptPaymentPlanPageVO> page = po.isPaging() ? po.getPageObj(BizReceiptPaymentPlanPageVO.class) : null;

        // 分页列表查询
        WmsQueryWrapper<BizReceiptPaymentPlanSearchPO> queryWrapper = new WmsQueryWrapper<>();
        queryWrapper.lambda()
                .eq(true, BizReceiptPaymentPlanSearchPO::getIsDelete, BizReceiptPaymentPlanHead.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptPaymentPlanSearchPO::getReceiptCode, BizReceiptPaymentPlanHead.class, po.getReceiptCode())
                .eq(UtilNumber.isNotEmpty(po.getFirstParty()), BizReceiptPaymentPlanSearchPO::getFirstParty, BizReceiptContractHead.class, po.getFirstParty())
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptPaymentPlanSearchPO::getReceiptCode, BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getPurchaseReceiptCode()), BizReceiptPaymentPlanSearchPO::getPurchaseReceiptCode, BizReceiptPaymentPlanItem.class, po.getPurchaseReceiptCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptPaymentPlanSearchPO::getContractName, BizReceiptContractHead.class, po.getContractName())
                .like(UtilString.isNotNullOrEmpty(po.getSendBatch()), BizReceiptPaymentPlanSearchPO::getBatchCode, BizReceiptDeliveryNoticeHead.class, po.getSendBatch())
                .in(UtilCollection.isNotEmpty(po.getPaymentMonth()), BizReceiptPaymentPlanSearchPO::getPaymentMonth, BizReceiptPaymentPlanHead.class, po.getPaymentMonth())
                .eq(UtilNumber.isNotEmpty(po.getPurchaseType()), BizReceiptPaymentPlanSearchPO::getPurchaseType, BizReceiptContractHead.class, po.getPurchaseType())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptPaymentPlanSearchPO::getReceiptStatus, BizReceiptPaymentPlanHead.class, po.getReceiptStatusList());
        if (UtilString.isNotNullOrEmpty(po.getContractCreateUserName())) {
            queryWrapper.like("contract_create_user.user_name", po.getContractCreateUserName());
        }
        List<BizReceiptPaymentPlanPageVO> resultList = bizReceiptPaymentPlanHeadDataWrap.getPageVo(page, queryWrapper);
        // 油品类设置甲方默认为华信，币种为pkr
        resultList.forEach(c -> {
            if (c.getPlanType().equals(3)) {
                c.setFirstParty(EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode()).setCurrency(EnumContractCurrency.PKR.getCode());
            }
        });
        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? Objects.requireNonNull(page).getTotal() : resultList.size()));
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptPaymentPlanHead head = bizReceiptPaymentPlanHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptPaymentPlanHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptPaymentPlanHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        this.fillData(headDTO);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);

        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO,
                new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    private void fillData(BizReceiptPaymentPlanHeadDTO headDTO) {
        List<Integer> purchaseTypeList = new ArrayList<>();
        purchaseTypeList.add(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode());
        purchaseTypeList.add(EnumPurchaseType.SERVICE.getCode());
        purchaseTypeList.add(EnumPurchaseType.CONSTRUCTION.getCode());
        if (headDTO.getPlanType().equals(2) && purchaseTypeList.contains(headDTO.getPurchaseType())) {
            List<BizReceiptContractSubItem> subItems = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda()
                    .eq(BizReceiptContractSubItem::getHeadId, headDTO.getContractId())
                    .eq(BizReceiptContractSubItem::getReceiveId, headDTO.getReceiveId())
            );
            headDTO.setSubItemList(subItems);
        }
        if (headDTO.getPlanType().equals(3)) {
            headDTO.setFirstParty(EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode());
            headDTO.setCurrency(EnumContractCurrency.PKR.getCode());
        }
        // 查询发运单号
        if (UtilCollection.isNotEmpty(headDTO.getItemList())) {
            List<String> purchaseCodeList = headDTO.getItemList().stream().map(BizReceiptPaymentPlanItemDTO::getPurchaseReceiptCode).filter(UtilString::isNotNullOrEmpty).collect(Collectors.toList());
            if (UtilCollection.isNotEmpty(purchaseCodeList)) {
                LambdaQueryWrapper<BizReceiptDeliveryNoticeHead> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(BizReceiptDeliveryNoticeHead::getPurchaseCode, purchaseCodeList);
                queryWrapper.ne(BizReceiptDeliveryNoticeHead::getBatchCode, Const.STRING_EMPTY);
                BizReceiptDeliveryNoticeHead deliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getOne(queryWrapper, false);
                if (UtilObject.isNotNull(deliveryNoticeHead)) {
                    headDTO.setSendBatch(deliveryNoticeHead.getBatchCode());
                }
            }
        }
    }

    /**
     * 按钮组
     *
     * @param headDTO 付款计划单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptPaymentPlanHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue().equals(receiptStatus)) {
            // 待编制 -【保存、提交、删除】
            return buttonVO.setButtonSave(true).setButtonSubmit(true).setButtonDelete(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_COMPILED.getValue().equals(receiptStatus)) {
            // 已编制 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        return buttonVO;
    }


    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        // if (UtilCollection.isEmpty(po.getItemList())) {
        //     throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        // }
    }

    /**
     * 保存-校验入参
     */
    public void checkSubmitData(BizContext ctx) {
        checkSaveData(ctx);
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptCapitalPlanHead capitalPlanHead = bizReceiptCapitalPlanHeadDataWrap.getOne(new QueryWrapper<BizReceiptCapitalPlanHead>().lambda()
                .eq(BizReceiptCapitalPlanHead::getFirstParty, po.getFirstParty())
                .eq(BizReceiptCapitalPlanHead::getPaymentMonth, po.getPaymentMonth()));

        if (Objects.isNull(capitalPlanHead)) {
            throw new WmsException("没有对应的资金计划");
        } else {
            if (!EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(capitalPlanHead.getReceiptStatus())
                    && !EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(capitalPlanHead.getReceiptStatus())) {
                throw new WmsException("本月资金计划已提交，请在次月编制付款计划");
            }
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSubmit(BizContext ctx) {
        List<Long> ids = ctx.getContextData(Const.BIZ_CONTEXT_KEY_IDS);
        List<BizReceiptPaymentPlanHead> bizReceiptPaymentPlanHeads = bizReceiptPaymentPlanHeadDataWrap.listByIds(ids);
        List<BizReceiptPaymentPlanHeadDTO> list = UtilCollection.toList(bizReceiptPaymentPlanHeads, BizReceiptPaymentPlanHeadDTO.class);
        dataFillService.fillAttr(list);
        for (BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO : list) {
            ctx.setPoContextData(paymentPlanHeadDTO);

            this.checkSubmitData(ctx);

            // 提交付款计划
            this.submit(ctx);

            // 加入资金计划
            this.joinCapitalPlan(ctx);

            // 保存操作日志
            this.saveBizReceiptOperationLog(ctx);
        }


    }

    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setNotPaidAmount(po.getQty());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {

            bizReceiptPaymentPlanHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        }
        /* ********************** head处理结束 *************************/
        /* ********************** item处理开始 *************************/
        if (UtilCollection.isNotEmpty(po.getItemList())) {
            for (BizReceiptPaymentPlanItemDTO itemDto : po.getItemList()) {
                itemDto.setId(null);
                itemDto.setHeadId(po.getId());
                itemDto.setCreateUserId(user.getId());
            }
        }
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(po.getItemList());
        /* ********************** item处理结束 *************************/
        // if (UtilCollection.isNotEmpty(po.getSubItemList())) {
        //     for (BizReceiptContractSubItem subItem : po.getSubItemList()) {
        //         subItem.setId(null);
        //         subItem.setPaymentPlanId(po.getId());
        //     }
        // }

        // 保存分项信息
        bizReceiptContractSubItemDataWrap.saveOrUpdateBatch(po.getSubItemList());
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }

    private void deleteItem(BizReceiptPaymentPlanHeadDTO headDTO) {
        UpdateWrapper<BizReceiptPaymentPlanItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptPaymentPlanItem::getHeadId,
                headDTO.getId());
        bizReceiptPaymentPlanItemDataWrap.physicalDelete(wrapper);
    }

    /**
     * 更新付款计划状态
     *
     * @param headDTO     付款计划head
     * @param itemDTOList 付款计划item
     */
    public void updateStatus(BizReceiptPaymentPlanHeadDTO headDTO, List<BizReceiptPaymentPlanItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    public void updateStatus(List<Long> ids, Integer status) {
        UpdateWrapper<BizReceiptPaymentPlanItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().in(BizReceiptPaymentPlanItem::getHeadId,
                ids).set(BizReceiptPaymentPlanItem::getItemStatus, status);
        bizReceiptPaymentPlanItemDataWrap.update(wrapper);

        UpdateWrapper<BizReceiptPaymentPlanHead> headUpdateWrapper = new UpdateWrapper<>();
        headUpdateWrapper.lambda().in(BizReceiptPaymentPlanHead::getId, ids).set(BizReceiptPaymentPlanHead::getReceiptStatus, status);
        bizReceiptPaymentPlanHeadDataWrap.update(headUpdateWrapper);
    }

    /**
     * 更新付款计划head状态
     *
     * @param headDto 付款计划head
     */
    private void updateHead(BizReceiptPaymentPlanHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptPaymentPlanHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新付款计划item状态
     *
     * @param itemDtoList 付款计划item
     */
    private void updateItem(List<BizReceiptPaymentPlanItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptPaymentPlanItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }

    /**
     * 保存操作日志
     *
     * @param ctx ctx
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {

        BizReceiptPaymentPlanHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(),
                operationLogType, "", ctx.getCurrentUser().getId());
    }

    /**
     * 设置单据流详情
     *
     * @param ctx 入参上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        BizResultVO<BizReceiptPaymentPlanHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
        // 更新付款计划head、item状态 - 已编制
        this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPILED.getValue());
    }

    /**
     * 撤销单据
     *
     * @param ctx ctx
     */
    @Transactional(rollbackFor = Exception.class)
    public void revoke(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        if (!po.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_COMPILED.getValue())) {
            return;
        }
        BizReceiptCapitalPlanHead capitalPlanHead = bizReceiptCapitalPlanHeadDataWrap.getOne(new QueryWrapper<BizReceiptCapitalPlanHead>().lambda()
                .eq(BizReceiptCapitalPlanHead::getFirstParty, po.getFirstParty())
                .eq(BizReceiptCapitalPlanHead::getPaymentMonth, po.getPaymentMonth()));
        // 只有资金计划为草稿或已驳回时方可撤销
        if (capitalPlanHead.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue())
                || capitalPlanHead.getReceiptStatus().equals(EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue())) {
            // 单据变为待编制
            // 清空资金计划单号
            po.setCapitalPlanCode(Const.STRING_EMPTY);
            this.updateStatus(po, po.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            // 对应的资金计划中删除改行付款计划；
            UpdateWrapper<BizReceiptCapitalPlanItem> wrapper = new UpdateWrapper<>();
            wrapper.lambda().eq(BizReceiptCapitalPlanItem::getPaymentPlanId, po.getId());
            bizReceiptCapitalPlanItemDataWrap.physicalDelete(wrapper);
        }
    }

    /**
     * 加入资金计划
     *
     * @param ctx
     */
    public void joinCapitalPlan(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentPlanHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        BizReceiptCapitalPlanHead capitalPlanHead = bizReceiptCapitalPlanHeadDataWrap.getOne(new QueryWrapper<BizReceiptCapitalPlanHead>().lambda()
                .eq(BizReceiptCapitalPlanHead::getFirstParty, po.getFirstParty())
                .eq(BizReceiptCapitalPlanHead::getPaymentMonth, po.getPaymentMonth()));
        BizReceiptCapitalPlanHeadDTO bizReceiptCapitalPlanHeadDTO = UtilBean.newInstance(capitalPlanHead, BizReceiptCapitalPlanHeadDTO.class);
        dataFillService.fillAttr(bizReceiptCapitalPlanHeadDTO);

        // 计算行序号
        String maxRid = "0";
        if(UtilCollection.isNotEmpty(bizReceiptCapitalPlanHeadDTO.getItemList())){
            maxRid = bizReceiptCapitalPlanHeadDTO.getItemList().stream().map(BizReceiptCapitalPlanItemDTO::getRid).max(Comparator.comparing(Integer::parseInt)).orElse(Const.STRING_EMPTY);
        }

        BizReceiptCapitalPlanItemDTO bizReceiptCapitalPlanItemDTO = new BizReceiptCapitalPlanItemDTO();
        bizReceiptCapitalPlanItemDTO.setId(null);
        bizReceiptCapitalPlanItemDTO.setHeadId(capitalPlanHead.getId());
        bizReceiptCapitalPlanItemDTO.setRid(String.valueOf(UtilCollection.isNotEmpty(bizReceiptCapitalPlanHeadDTO.getItemList()) ? Integer.parseInt(maxRid) + 1 : 1));
        bizReceiptCapitalPlanItemDTO.setPaymentPlanId(po.getId());
        bizReceiptCapitalPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_COMPILED.getValue());
        bizReceiptCapitalPlanItemDataWrap.saveDto(bizReceiptCapitalPlanItemDTO);
        // 回填资金计划单号
        po.setCapitalPlanCode(capitalPlanHead.getReceiptCode());
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(po);
    }

    /**
     * 生成付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void generatePaymentPlan(BizContext ctx) {
        BizReceiptContractHeadDTO contractHeadDTO = ctx.getPoContextData();

        dataFillService.fillAttr(contractHeadDTO);

        String supplierCode = contractHeadDTO.getSupplierCode();
        Integer firstParty = contractHeadDTO.getFirstParty();

        // 合同甲方为华信资源有限公司
        if (EnumContractFirstParty.SHANGHAI_ELECTRIC.equals(EnumContractFirstParty.getByValue(firstParty))) {
            Integer receiptType = contractHeadDTO.getReceiptType();
            Integer purchaseType = contractHeadDTO.getPurchaseType();

            // 生产物资类
            if (EnumPurchaseType.PRODUCTION_MATERIAL.equals(EnumPurchaseType.getByValue(purchaseType))
                    && EnumReceiptType.OTHER_CONTRACT.getValue().equals(receiptType)) {
                this.processPaymentNodes(contractHeadDTO, purchaseType);
            }

            // 合同里带有送货单号,判断是不是已完成单
            if (UtilString.isNotNullOrEmpty(contractHeadDTO.getDeliveryCode())) {
                BizReceiptDeliveryNoticeHead deliveryNoticeHead = bizReceiptDeliveryNoticeHeadDataWrap.getOne(new QueryWrapper<BizReceiptDeliveryNoticeHead>().lambda()
                        .eq(BizReceiptDeliveryNoticeHead::getReceiptCode, contractHeadDTO.getDeliveryCode())
                        .eq(BizReceiptDeliveryNoticeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
                if (deliveryNoticeHead != null) {
                    List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();
                    this.savePaymentPlanByContract(contractHeadDTO, paymentPlanItemDTOS, deliveryNoticeHead.getId(), false);
                }
            }
        }

        // 合同甲方为能殷
        if (EnumContractFirstParty.SHANGHAI_ENERGY.equals(EnumContractFirstParty.getByValue(firstParty))) {
            Integer receiptType = contractHeadDTO.getReceiptType();
            Integer purchaseType = contractHeadDTO.getPurchaseType();

            // 生产物资类且供应商不是指定公司
            if (EnumPurchaseType.PRODUCTION_MATERIAL.equals(EnumPurchaseType.getByValue(purchaseType))
                    && EnumReceiptType.OTHER_CONTRACT.getValue().equals(receiptType)
                    && !SUPPLIER_CODE_600151.equals(supplierCode)
                    && !SUPPLIER_CODE_600159.equals(supplierCode)) {
                this.processPaymentNodes(contractHeadDTO, purchaseType);
            }

            // 资产类的其他合同
            if (EnumPurchaseType.ASSET.equals(EnumPurchaseType.getByValue(purchaseType))
                    && EnumReceiptType.OTHER_CONTRACT.getValue().equals(receiptType)) {
                this.processPaymentNodes(contractHeadDTO, purchaseType);
            }
        }
    }

    private static final String SUPPLIER_CODE_600151 = "600151";
    private static final String SUPPLIER_CODE_600159 = "600159";

    /**
     * 处理付款节点逻辑
     */
    private void processPaymentNodes(BizReceiptContractHeadDTO contractHeadDTO, Integer purchaseType) {
        List<BizReceiptContractPaymentNodeDTO> nodeList = contractHeadDTO.getNodeList();
        if (UtilCollection.isNotEmpty(nodeList)) {
            nodeList.stream()
                    .filter(node ->
                            EnumContractPaymentNode.NODE_1.equals(EnumContractPaymentNode.getByCode(node.getPaymentNode()))
                            || EnumContractPaymentNode.NODE_2.equals(EnumContractPaymentNode.getByCode(node.getPaymentNode())))
                    .forEach(node -> this.savePaymentPlanByNode(node, contractHeadDTO, purchaseType));
        }
    }


    /**
     * 自动生成定额付款计划
     * 当合同甲方为能毁，乙方为600151中租益联建筑工程有限公司或乙方为600159上海筑驰实业发展有限公司时，
     * 付款预算不根据付款节点生成，需通过定时任务，每月25号凌晨3点，自动生成每个合同的定额付款预算;---定额都先默认100
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoGeneratePaymentPlan() {
        // List<Integer> purchaseTypeList = new ArrayList<>();
        // purchaseTypeList.add(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode());
        // purchaseTypeList.add(EnumPurchaseType.SERVICE.getCode());
        // purchaseTypeList.add(EnumPurchaseType.CONSTRUCTION.getCode());
        // List<BizReceiptContractHead> bizReceiptContractHeads = bizReceiptContractHeadDataWrap.list(
        //         new QueryWrapper<BizReceiptContractHead>().lambda()
        //                 .eq(BizReceiptContractHead::getFirstParty, EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode())
        //                 .eq(BizReceiptContractHead::getIsDelete, EnumRealYn.FALSE.getIntValue())
        //                 .eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
        //                 .eq(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue())
        //                 .in(BizReceiptContractHead::getPurchaseType, purchaseTypeList));
        // 600151中租益联建筑工程有限公司
        // 600159上海筑驰实业发展有限公司
        List<DicSupplier> dicSuppliers = dicSupplierDataWrap.list(new QueryWrapper<DicSupplier>().lambda().in(DicSupplier::getSupplierCode, Arrays.asList("600151", "600159")));
        if (UtilCollection.isNotEmpty(dicSuppliers)) {
            List<BizReceiptContractHead> bizReceiptContractHeadList = bizReceiptContractHeadDataWrap.list(
                    new QueryWrapper<BizReceiptContractHead>().lambda()
                            .eq(BizReceiptContractHead::getFirstParty, EnumContractFirstParty.SHANGHAI_ENERGY.getCode())
                            .eq(BizReceiptContractHead::getIsDelete, EnumRealYn.FALSE.getIntValue())
                            .in(BizReceiptContractHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), EnumReceiptStatus.RECEIPT_STATUS_INS_EXECUTION.getValue()))
                            .eq(BizReceiptContractHead::getPurchaseType, EnumPurchaseType.PRODUCTION_MATERIAL.getCode())
                            .eq(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue())
                            .in(BizReceiptContractHead::getSupplierId, dicSuppliers.stream().map(DicSupplier::getId).collect(Collectors.toList())));
            List<BizReceiptContractHeadDTO> contractHeadDTOS = UtilCollection.toList(bizReceiptContractHeadList, BizReceiptContractHeadDTO.class);
            dataFillService.fillAttr(contractHeadDTOS);
            List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();
            for (BizReceiptContractHeadDTO bizReceiptContractHead : contractHeadDTOS) {
                this.savePaymentPlanByContract(bizReceiptContractHead, paymentPlanItemDTOS, null, true);
            }
        }
    }

    /**
     * 补充生成其他合同付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void addGeneratePaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }
        List<BizReceiptContractHead> headList = bizReceiptContractHeadDataWrap.list(
                new QueryWrapper<BizReceiptContractHead>().lambda()
                        .eq(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue())
                        .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptContractHead::getReceiptCode, receiptCodeList));
        List<BizReceiptContractHeadDTO> headDTOList = UtilCollection.toList(headList, BizReceiptContractHeadDTO.class);
        for (BizReceiptContractHeadDTO headDTO : headDTOList) {
            BizContext ctx = new BizContext();
            ctx.setPoContextData(headDTO);
            this.generatePaymentPlan(ctx);
        }
    }

    /**
     * 补充生成其他合同付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void addGenerateDeliveryNoticePaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }
        List<BizReceiptDeliveryNoticeHead> headList = bizReceiptDeliveryNoticeHeadDataWrap.list(
                new QueryWrapper<BizReceiptDeliveryNoticeHead>().lambda()
                        .eq(BizReceiptDeliveryNoticeHead::getReceiptType, EnumReceiptType.DELIVERY_NOTICE.getValue())
                        .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptDeliveryNoticeHead::getReceiptCode, receiptCodeList)
                        .in(BizReceiptDeliveryNoticeHead::getSendType, Arrays.asList(EnumSendType.OFFSHORE_PROCUREMENT.getValue(), EnumSendType.ONSHORE_PROCUREMENT.getValue(), EnumSendType.INLAND_PROCUREMENT.getValue()))
                        .eq(BizReceiptDeliveryNoticeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        List<BizReceiptDeliveryNoticeHeadDTO> headDTOList = UtilCollection.toList(headList, BizReceiptDeliveryNoticeHeadDTO.class);
        dataFillService.fillAttr(headDTOList);
        for (BizReceiptDeliveryNoticeHeadDTO headDTO : headDTOList) {
            BizContext ctx = new BizContext();
            ctx.setPoContextData(headDTO);
            deliveryNoticeComponent.genPaymentPlan(ctx);
        }
    }

    /**
     * 补充生成其他合同付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void addGenerateInspectInputPaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }
        List<BizReceiptInputHead> headList = bizReceiptInputHeadDataWrap.list(
                new QueryWrapper<BizReceiptInputHead>().lambda()
                        .eq(BizReceiptInputHead::getReceiptType, EnumReceiptType.STOCK_INPUT_INSPECT.getValue())
                        .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptInputHead::getReceiptCode, receiptCodeList)
                        .in(BizReceiptInputHead::getSendType, Arrays.asList(EnumSendType.OFFSHORE_PROCUREMENT.getValue(), EnumSendType.ONSHORE_PROCUREMENT.getValue(), EnumSendType.INLAND_PROCUREMENT.getValue()))
                        .eq(BizReceiptInputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
        List<BizReceiptInputHeadDTO> headDTOList = UtilCollection.toList(headList, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(headDTOList);
        for (BizReceiptInputHeadDTO headDTO : headDTOList) {
            BizContext ctx = new BizContext();
            ctx.setPoContextData(headDTO);
            inspectInputComponent.genPaymentPlan(ctx);
        }
    }

    /**
     * 补充生成其他合同付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void addGenerateContractReceivingPaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }
        List<BizReceiptContractReceivingHead> headList = bizReceiptContractReceivingHeadDataWrap.list(
                new QueryWrapper<BizReceiptContractReceivingHead>().lambda()
                        .eq(BizReceiptContractReceivingHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                        .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptContractReceivingHead::getReceiptCode, receiptCodeList));
        List<BizReceiptContractReceivingHeadDTO> headDTOList = UtilCollection.toList(headList, BizReceiptContractReceivingHeadDTO.class);
        dataFillService.fillAttr(headDTOList);
        for (BizReceiptContractReceivingHeadDTO headDTO : headDTOList) {
            BizContext ctx = new BizContext();
            ctx.setPoContextData(headDTO);
            ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_POSTING);
            contractReceivingComponent.genPaymentPlan(ctx);
        }
    }

    /**
     * 生成柴油与重油的付款计划；柴油按车，重油按吨；
     */
    @Transactional(rollbackFor = Exception.class)
    public void autoGenerateOilPaymentPlan() {
        // 日志记录
        log.info("开始自动生成油品付款计划");

        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        List<BizReceiptPaymentPlanItemDTO> itemList = new ArrayList<>();

        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(1L);
        paymentPlanHeadDTO.setPlanType(3);
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());

        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        // 定义物料编码常量
        final String DIESEL_CODE = "Y201210001";
        final String HEAVY_OIL_CODE = "Y201110001";

        DicMaterialDTO diesel = dictionaryService.getMatCacheById(dictionaryService.getMatIdByMatCode(DIESEL_CODE));
        DicMaterialDTO heavyOil = dictionaryService.getMatCacheById(dictionaryService.getMatIdByMatCode(HEAVY_OIL_CODE));

        // 构建并添加 Item
        AtomicInteger rid = new AtomicInteger(1);
        itemList.add(createItemDTO(paymentPlanHeadDTO.getId(), diesel, rid));
        itemList.add(createItemDTO(paymentPlanHeadDTO.getId(), heavyOil, rid));

        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(itemList);

        log.info("油品付款计划生成完成");
    }

    // 公共字段构建方法
    private BizReceiptPaymentPlanItemDTO createItemDTO(Long headId, DicMaterialDTO materialDTO, AtomicInteger rid) {
        BizReceiptPaymentPlanItemDTO itemDTO = new BizReceiptPaymentPlanItemDTO();
        itemDTO.setId(null);
        itemDTO.setHeadId(headId);
        itemDTO.setRid(String.valueOf(rid.getAndIncrement()));
        itemDTO.setMatId(materialDTO.getId());
        itemDTO.setUnitId(materialDTO.getUnitId());
        itemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        itemDTO.setCreateUserId(1L);
        return itemDTO;
    }


    /**
     * @param bizReceiptContractHead 合同
     * @param paymentPlanItemDTOS    付款项
     * @param deliveryHeadId         送货通知单ID
     * @param isQuota                是否定额
     */
    public void savePaymentPlanByContract(BizReceiptContractHeadDTO bizReceiptContractHead, List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS, Long deliveryHeadId, Boolean isQuota) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(UtilNumber.isNotEmpty(bizReceiptContractHead.getCreateUserId()) ? bizReceiptContractHead.getCreateUserId() : 1L);
        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(bizReceiptContractHead.getPurchaseType()));
        paymentPlanHeadDTO.setTaxCodeRate(bizReceiptContractHead.getItemList().get(0).getTaxCodeRate());
        paymentPlanHeadDTO.setContractAmount(bizReceiptContractHead.getItemList().stream().map(BizReceiptContractItemDTO::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        paymentPlanHeadDTO.setContractId(bizReceiptContractHead.getId());
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        // 查询送货单对应的入库单行项目信息
        Map<String, BizReceiptInputItemDTO> inputReferMap = new HashMap<>();
        Map<String, BizReceiptInspectItemDTO> inspectItemMap = new HashMap<>();
        if (UtilNumber.isNotEmpty(deliveryHeadId)) {
            LambdaQueryWrapper<BizReceiptDeliveryNoticeItem> deliveryNoticeItemWrapper = new LambdaQueryWrapper<>();
            deliveryNoticeItemWrapper.eq(BizReceiptDeliveryNoticeItem::getHeadId, deliveryHeadId);
            List<BizReceiptDeliveryNoticeItem> deliveryNoticeItemList = bizReceiptDeliveryNoticeItemDataWrap.list(deliveryNoticeItemWrapper);
            if (UtilCollection.isNotEmpty(deliveryNoticeItemList)) {
                List<Long> referReceiptItemIdList = deliveryNoticeItemList.stream().map(BizReceiptDeliveryNoticeItem::getReferReceiptItemId).collect(Collectors.toList());
                LambdaQueryWrapper<BizReceiptInputItem> inputItemQueryWrapper = new LambdaQueryWrapper<>();
                inputItemQueryWrapper.eq(BizReceiptInputItem::getDeliveryNoticeHeadId, deliveryHeadId);
                inputItemQueryWrapper.in(BizReceiptInputItem::getReferReceiptItemId, referReceiptItemIdList);
                List<BizReceiptInputItem> inputItemList = bizReceiptInputItemDataWrap.list(inputItemQueryWrapper);
                List<BizReceiptInputItemDTO> inputItemDTOList = UtilCollection.toList(inputItemList, BizReceiptInputItemDTO.class);
                dataFillService.fillAttr(inputItemDTOList);
                inputReferMap = inputItemDTOList.stream().collect(Collectors.toMap(item -> item.getPurchaseCode() + Const.HYPHEN + item.getPurchaseRid(), obj -> obj, (v1, v2) -> v1));
                LambdaQueryWrapper<BizReceiptInspectItem> inspectItemWrapper = new LambdaQueryWrapper<>();
                inspectItemWrapper.in(BizReceiptInspectItem::getReferReceiptItemId, referReceiptItemIdList);
                inspectItemWrapper.in(BizReceiptInspectItem::getDeliveryItemId, deliveryNoticeItemList.stream().map(BizReceiptDeliveryNoticeItem::getId).collect(Collectors.toList()));
                List<BizReceiptInspectItem> inspectItemList = bizReceiptInspectItemDataWrap.list(inspectItemWrapper);
                List<BizReceiptInspectItemDTO> inspectItemDTOList = UtilCollection.toList(inspectItemList, BizReceiptInspectItemDTO.class);
                dataFillService.fillAttr(inspectItemDTOList);
                inspectItemMap = inspectItemDTOList.stream().collect(Collectors.toMap(item -> item.getPurchaseCode() + Const.HYPHEN + item.getPurchaseRid(), obj -> obj, (v1, v2) -> v1));
            }
        }

        BigDecimal zero = BigDecimal.ZERO;
        for (BizReceiptContractItemDTO bizReceiptContractItemDTO : bizReceiptContractHead.getItemList()) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(bizReceiptContractItemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setContractQty(bizReceiptContractItemDTO.getQty());
            // 定额数据用户暂未提供，开发时可默认100
            paymentPlanItemDTO.setQty(isQuota ? new BigDecimal("100") : bizReceiptContractItemDTO.getQty());
            paymentPlanItemDTO.setCreateUserId(UtilNumber.isNotEmpty(bizReceiptContractItemDTO.getCreateUserId()) ? bizReceiptContractItemDTO.getCreateUserId() : 1L);
            // 取内贸送货单上的采购订单号
            BizReceiptDeliveryNoticeItem deliveryNoticeItem = bizReceiptDeliveryNoticeItemDataWrap.getById(bizReceiptContractItemDTO.getPreReceiptItemId());
            if (Objects.nonNull(deliveryNoticeItem)) {
                paymentPlanItemDTO.setPurchaseReceiptCode(deliveryNoticeItem.getPurchaseCode());
                paymentPlanItemDTO.setPurchaseReceiptRid(deliveryNoticeItem.getPurchaseRid());
                // 赋值质检会签、验收入库单据信息
                if (inspectItemMap.containsKey(deliveryNoticeItem.getPurchaseCode() + Const.HYPHEN + deliveryNoticeItem.getPurchaseRid())) {
                    BizReceiptInspectItemDTO inspectItemDTO = inspectItemMap.get(deliveryNoticeItem.getPurchaseCode() + Const.HYPHEN + deliveryNoticeItem.getPurchaseRid());
                    paymentPlanItemDTO.setSignInspectReceiptId(inspectItemDTO.getHeadId());
                    paymentPlanItemDTO.setSignInspectReceiptCode(inspectItemDTO.getReceiptCode());
                    paymentPlanItemDTO.setSignInspectReceiptType(inspectItemDTO.getReceiptType());
                    paymentPlanItemDTO.setQualifiedQty(inspectItemDTO.getQty());
                }
                if (inputReferMap.containsKey(deliveryNoticeItem.getPurchaseCode() + Const.HYPHEN + deliveryNoticeItem.getPurchaseRid())) {
                    BizReceiptInputItemDTO inputItemDTO = inputReferMap.get(deliveryNoticeItem.getPurchaseCode() + Const.HYPHEN + deliveryNoticeItem.getPurchaseRid());
                    paymentPlanItemDTO.setInputReceiptId(inputItemDTO.getHeadId());
                    paymentPlanItemDTO.setInputReceiptCode(inputItemDTO.getReceiptCode());
                    paymentPlanItemDTO.setInputReceiptType(inputItemDTO.getReceiptType());
                    paymentPlanItemDTO.setInputQty(inputItemDTO.getQty());
                    paymentPlanItemDTO.setMatDocCode(inputItemDTO.getMatDocCode());
                    paymentPlanItemDTO.setMatDocRid(inputItemDTO.getMatDocRid());
                    paymentPlanItemDTO.setMatDocYear(inputItemDTO.getMatDocYear());
                }
            }
            paymentPlanItemDTO.setPreReceiptType(bizReceiptContractHead.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(bizReceiptContractItemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(bizReceiptContractItemDTO.getHeadId());
            paymentPlanItemDTOS.add(paymentPlanItemDTO);
            zero = zero.add(paymentPlanItemDTO.getQty().multiply(bizReceiptContractItemDTO.getTaxPrice()));
        }
        paymentPlanHeadDTO.setQty(zero.setScale(2, RoundingMode.HALF_UP));
        // 更新清款金额
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
        paymentPlanItemDTOS.clear();
    }

    public Integer getPlanTypeByPurchaseType(Integer purchaseType) {

        List<Integer> list = new ArrayList<>();
        list.add(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode());
        list.add(EnumPurchaseType.SERVICE.getCode());
        list.add(EnumPurchaseType.CONSTRUCTION.getCode());
        if (list.contains(purchaseType)) {
            return 2;
        }
        if (EnumPurchaseType.ASSET.getCode().equals(purchaseType)) {
            return 4;
        }
        return 1;
    }

    private void savePaymentPlanByNode(BizReceiptContractPaymentNodeDTO node, BizReceiptContractHeadDTO contractHeadDTO, Integer purchaseType) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(UtilNumber.isNotEmpty(contractHeadDTO.getCreateUserId()) ? contractHeadDTO.getCreateUserId() : 1L);

        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(contractHeadDTO.getPurchaseType()));

        paymentPlanHeadDTO.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
        paymentPlanHeadDTO.setContractAmount(contractHeadDTO.getItemList().stream().map(BizReceiptContractItemDTO::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        // 预付款：合同含税总价*付款比例
        paymentPlanHeadDTO.setQty(node.getRate().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).multiply(contractHeadDTO.getItemList().stream().map(BizReceiptContractItemDTO::getTaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        paymentPlanHeadDTO.setContractId(contractHeadDTO.getId());
        paymentPlanHeadDTO.setPaymentNode(node.getPaymentNode());
        paymentPlanHeadDTO.setRate(node.getRate());
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        List<BizReceiptPaymentPlanItemDTO> itemDTOS = new ArrayList<>();

        for (BizReceiptContractItemDTO itemDTO : contractHeadDTO.getItemList()) {
            BizReceiptPaymentPlanItemDTO item = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, item);
            item.setId(null);
            item.setHeadId(paymentPlanHeadDTO.getId());
            item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            item.setQty(itemDTO.getQty());
            item.setCreateUserId(1L);
            item.setPreReceiptType(contractHeadDTO.getReceiptType());
            item.setPreReceiptItemId(itemDTO.getId());
            item.setPreReceiptHeadId(itemDTO.getHeadId());
            if (EnumContractPaymentNode.NODE_1.getCode().equals(node.getPaymentNode())) {
                item.setQty(BigDecimal.ZERO);
                item.setInputQty(BigDecimal.ZERO);
            }
            itemDTOS.add(item);
        }
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(itemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : itemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 获取下个月字符串
     *
     * @return
     */
    private String getMonth() {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取下个月
        YearMonth nextMonth = YearMonth.from(currentDate).plusMonths(1);
        // 格式化为 "yyyy-MM" 的字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        return nextMonth.format(formatter);

    }


    // public void genNode4PaymentPlan() {
    //     // 查询所有具有到货款节点的付款节点
    //     List<BizReceiptContractPaymentNode> paymentNodes = bizReceiptContractPaymentNodeDataWrap.list(new QueryWrapper<BizReceiptContractPaymentNode>().lambda().eq(BizReceiptContractPaymentNode::getPaymentNode, EnumContractPaymentNode.NODE_4.getCode()));
    //     Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap = paymentNodes.stream().collect(Collectors.groupingBy(BizReceiptContractPaymentNode::getHeadId));
    //     List<Long> contractIds = paymentNodes.stream().map(BizReceiptContractPaymentNode::getHeadId).collect(Collectors.toList());
    //     if (UtilCollection.isEmpty(contractIds)) {
    //         return;
    //     }
    //     // 离岸和内贸
    //     List<Integer> sendTypeList = Arrays.asList(EnumSendType.OFFSHORE_PROCUREMENT.getValue(), EnumSendType.INLAND_PROCUREMENT.getValue());
    //     List<BizReceiptContractHead> bizReceiptContractHeadList = bizReceiptContractHeadDataWrap.listByIds(contractIds);
    //     Map<String, List<BizReceiptContractHead>> contractMap = bizReceiptContractHeadList.stream().collect(Collectors.groupingBy(BizReceiptContractHead::getReceiptCode));
    //     // 已完成的托收Po批次,内贸送货
    //     List<BizReceiptDeliveryNoticeHead> deliveryNoticeHeads = bizReceiptDeliveryNoticeHeadDataWrap.list(new QueryWrapper<BizReceiptDeliveryNoticeHead>().lambda()
    //             .in(BizReceiptDeliveryNoticeHead::getSendType, sendTypeList)
    //             .eq(BizReceiptDeliveryNoticeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue()));
    //     List<BizReceiptDeliveryNoticeHeadDTO> deliveryNoticeHeadDTOS = UtilCollection.toList(deliveryNoticeHeads, BizReceiptDeliveryNoticeHeadDTO.class);
    //     dataFillService.fillAttr(deliveryNoticeHeadDTOS);
    //     // 符合条件的送货行
    //     List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemDTOList = new ArrayList<>();
    //     for (BizReceiptDeliveryNoticeHeadDTO deliveryNoticeHeadDTO : deliveryNoticeHeadDTOS) {
    //         List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemDTOS = deliveryNoticeHeadDTO.getItemList().stream().filter(c -> contractMap.containsKey(c.getReceiptCode())).collect(Collectors.toList());
    //         deliveryNoticeItemDTOList.addAll(deliveryNoticeItemDTOS);
    //     }
    //     // 按照行项目的合同号分组
    //     Map<String, List<BizReceiptDeliveryNoticeItemDTO>> contractCodeMap = deliveryNoticeItemDTOList.stream().collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getContractCode));
    //     contractCodeMap.forEach((k, v) -> {
    //         BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getOne(new QueryWrapper<BizReceiptContractHead>().lambda().eq(BizReceiptContractHead::getReceiptCode, k));
    //         BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
    //         dataFillService.fillAttr(contractHeadDTO);
    //         BizReceiptContractPaymentNode paymentNode = payNodeMap.get(contractHead.getId()).get(0);
    //         Integer paymentDay = paymentNode.getPaymentCondition();
    //         // 更新时间加上付款条件里的天数，筛选出符合条件的
    //         List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemDTOS = v.stream().filter(c -> isSameDay(UtilDate.plusDays(c.getModifyTime(), paymentDay), new Date())).collect(Collectors.toList());
    //         // 生成到货款付款计划
    //         this.generatePaymentPlanForDelivery(contractHeadDTO, paymentNode, deliveryNoticeItemDTOS);
    //
    //     });
    //     // 查找所有带有送货单号的合同（华信对能殷）
    //     // 过滤出带有到货款支付节点的，并且当前日期是提交时间+支付天数
    //     List<BizReceiptContractHead> bizReceiptContractHeads = bizReceiptContractHeadDataWrap.list(new QueryWrapper<BizReceiptContractHead>().gt("length(delivery_code)", 1))
    //             .stream().filter(c -> contractIds.contains(c.getId()) &&
    //                     isSameDay(UtilDate.plusDays(c.getModifyTime(), payNodeMap.get(c.getId()).get(0).getPaymentCondition()), new Date())).collect(Collectors.toList());
    //     List<BizReceiptContractHeadDTO> contractHeadDTOS = UtilCollection.toList(bizReceiptContractHeads, BizReceiptContractHeadDTO.class);
    //     for (BizReceiptContractHeadDTO bizReceiptContractHead : contractHeadDTOS) {
    //         BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
    //         paymentPlanHeadDTO.setId(null);
    //         paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
    //         paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
    //         paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
    //         paymentPlanHeadDTO.setCreateUserId(1L);
    //         paymentPlanHeadDTO.setContractId(bizReceiptContractHead.getId());
    //         paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
    //         BizReceiptContractPaymentNode paymentNode = payNodeMap.get(bizReceiptContractHead.getId()).get(0);
    //         paymentPlanHeadDTO.setPaymentNode(paymentNode.getPaymentNode());
    //         paymentPlanHeadDTO.setRate(paymentNode.getRate());
    //
    //         if (UtilCollection.isNotEmpty(bizReceiptContractHead.getItemList())) {
    //             paymentPlanHeadDTO.setTaxCodeRate(bizReceiptContractHead.getItemList().get(0).getTaxCodeRate());
    //             paymentPlanHeadDTO.setContractAmount(bizReceiptContractHead.getItemList().stream()
    //                     .map(BizReceiptContractItemDTO::getTaxAmount)
    //                     .reduce(BigDecimal.ZERO, BigDecimal::add));
    //         }
    //
    //         bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);
    //
    //         BigDecimal totalAmount = BigDecimal.ZERO;
    //         List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();
    //
    //         for (BizReceiptContractItemDTO itemDTO : bizReceiptContractHead.getItemList()) {
    //             BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
    //             UtilBean.copy(itemDTO, paymentPlanItemDTO);
    //             paymentPlanItemDTO.setId(null);
    //             paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
    //             paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
    //             paymentPlanItemDTO.setQty(itemDTO.getQty());
    //             paymentPlanItemDTO.setCreateUserId(1L);
    //             // 已经生成采购订单了
    //             paymentPlanItemDTO.setPurchaseReceiptCode(itemDTO.getPurchaseReceiptCode());
    //             paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseReceiptRid());
    //             paymentPlanItemDTOS.add(paymentPlanItemDTO);
    //
    //             if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
    //                 totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
    //             }
    //         }
    //
    //         paymentPlanHeadDTO.setQty(totalAmount);
    //         bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
    //         bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
    //     }
    //
    //
    // }

    private void generatePaymentPlanForDelivery(BizReceiptContractHeadDTO contractHeadDTO, BizReceiptContractPaymentNode paymentNode, List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemDTOS) {

        if (UtilCollection.isEmpty(deliveryNoticeItemDTOS)) {
            return;
        }
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(1L);

        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(contractHeadDTO.getPurchaseType()));

        paymentPlanHeadDTO.setContractId(contractHeadDTO.getId());
        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        paymentPlanHeadDTO.setPaymentNode(paymentNode.getPaymentNode());
        paymentPlanHeadDTO.setRate(paymentNode.getRate());

        if (UtilCollection.isNotEmpty(contractHeadDTO.getItemList())) {
            paymentPlanHeadDTO.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
            paymentPlanHeadDTO.setContractAmount(contractHeadDTO.getItemList().stream()
                    .map(BizReceiptContractItemDTO::getTaxAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();

        for (BizReceiptDeliveryNoticeItemDTO itemDTO : deliveryNoticeItemDTOS) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setQty(itemDTO.getQty());
            paymentPlanItemDTO.setCreateUserId(1L);
            // 已经生成采购订单了
            paymentPlanItemDTO.setPurchaseReceiptCode(itemDTO.getPurchaseCode());
            paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseRid());
            paymentPlanItemDTO.setPreReceiptType(itemDTO.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(itemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(itemDTO.getHeadId());
            paymentPlanItemDTOS.add(paymentPlanItemDTO);

            if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
                totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
            }
        }

        paymentPlanHeadDTO.setQty(totalAmount.multiply(paymentNode.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 质保金：入库完成+质保期
     */
    public void genNode6PaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }

        // 查询所有具有质保金节点的付款节点
        List<BizReceiptContractPaymentNode> paymentNodes = bizReceiptContractPaymentNodeDataWrap.list(new QueryWrapper<BizReceiptContractPaymentNode>().lambda().eq(BizReceiptContractPaymentNode::getPaymentNode, EnumContractPaymentNode.NODE_6.getCode()));
        Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap = paymentNodes.stream().collect(Collectors.groupingBy(BizReceiptContractPaymentNode::getHeadId));

        // 已完成的验收入库单，排除油品送货的
        List<BizReceiptInputHead> inputHeads = bizReceiptInputHeadDataWrap.list(new QueryWrapper<BizReceiptInputHead>().lambda()
                .ne(BizReceiptInputHead::getSendType, EnumSendType.OIL_PROCUREMENT.getValue())
                .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptInputHead::getReceiptCode, receiptCodeList)
                .eq(BizReceiptInputHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
                .eq(BizReceiptInputHead::getReceiptType, EnumReceiptType.STOCK_INPUT_INSPECT.getValue()));
        List<BizReceiptInputHeadDTO> inputHeadDTOS = UtilCollection.toList(inputHeads, BizReceiptInputHeadDTO.class);
        dataFillService.fillAttr(inputHeadDTOS);

        List<BizReceiptInputItemDTO> inputItemDTOS = new ArrayList<>();
        for (BizReceiptInputHeadDTO headDTO : inputHeadDTOS) {
            List<BizReceiptInputItemDTO> filteredItems = headDTO.getItemList().stream()
                    .filter(item -> payNodeMap.containsKey(item.getReferReceiptHeadId()))
                    .collect(Collectors.toList());
            inputItemDTOS.addAll(filteredItems);
        }
        // 按照合同 id 分组
        Map<Long, List<BizReceiptInputItemDTO>> contractIdMap = inputItemDTOS.stream()
                .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getReferReceiptHeadId));

        contractIdMap.forEach((k, v) -> {
            // 按照入库id 分组
            Map<Long, List<BizReceiptInputItemDTO>> collect = v.stream()
                    .collect(Collectors.groupingBy(BizReceiptInputItemDTO::getHeadId));


            BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(k);

            BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
            dataFillService.fillAttr(contractHeadDTO);
            List<BizReceiptContractPaymentNode> nodes = payNodeMap.get(contractHead.getId());

            BizReceiptContractPaymentNode paymentNode = nodes.get(0);

            collect.forEach((k1, v1) -> {
                BizReceiptInputHead inputHead = bizReceiptInputHeadDataWrap.getById(k1);
                if (isSameDay(new Date(), UtilDate.plusDays(inputHead.getSubmitTime(), paymentNode.getPaymentCondition())) && UtilCollection.isNotEmpty(v1)) {

                    this.generatePaymentPlanForInput(v1, contractHead, paymentNode, contractHeadDTO);
                }
            });

        });

    }

    private void generatePaymentPlanForInput(List<BizReceiptInputItemDTO> v1, BizReceiptContractHead contractHead, BizReceiptContractPaymentNode paymentNode, BizReceiptContractHeadDTO contractHeadDTO) {
        BizReceiptPaymentPlanHeadDTO paymentPlanHeadDTO = new BizReceiptPaymentPlanHeadDTO();
        paymentPlanHeadDTO.setId(null);
        paymentPlanHeadDTO.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        paymentPlanHeadDTO.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        paymentPlanHeadDTO.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        paymentPlanHeadDTO.setCreateUserId(1L);
        paymentPlanHeadDTO.setContractId(contractHead.getId());

        paymentPlanHeadDTO.setPlanType(this.getPlanTypeByPurchaseType(contractHead.getPurchaseType()));

        paymentPlanHeadDTO.setPaymentMonth(this.getMonth());
        paymentPlanHeadDTO.setPaymentNode(paymentNode.getPaymentNode());
        paymentPlanHeadDTO.setRate(paymentNode.getRate());

        if (UtilCollection.isNotEmpty(contractHeadDTO.getItemList())) {
            paymentPlanHeadDTO.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
            paymentPlanHeadDTO.setContractAmount(contractHeadDTO.getItemList().stream()
                    .map(BizReceiptContractItemDTO::getTaxAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        bizReceiptPaymentPlanHeadDataWrap.saveDto(paymentPlanHeadDTO);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> paymentPlanItemDTOS = new ArrayList<>();

        for (BizReceiptInputItemDTO itemDTO : v1) {
            BizReceiptPaymentPlanItemDTO paymentPlanItemDTO = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, paymentPlanItemDTO);
            paymentPlanItemDTO.setId(null);
            paymentPlanItemDTO.setHeadId(paymentPlanHeadDTO.getId());
            paymentPlanItemDTO.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            paymentPlanItemDTO.setQty(itemDTO.getQty());
            paymentPlanItemDTO.setCreateUserId(1L);
            // 已经生成采购订单了
            paymentPlanItemDTO.setPurchaseReceiptCode(itemDTO.getPurchaseCode());
            paymentPlanItemDTO.setPurchaseReceiptRid(itemDTO.getPurchaseRid());
            // 质检会签单号
            paymentPlanItemDTO.setSignInspectReceiptCode(itemDTO.getPreReceiptCode());
            paymentPlanItemDTO.setSignInspectReceiptId(itemDTO.getPreReceiptHeadId());
            paymentPlanItemDTO.setSignInspectReceiptType(EnumReceiptType.SIGN_INSPECTION_PURCHASE.getValue());
            // 合格数量
            paymentPlanItemDTO.setQualifiedQty(itemDTO.getQty());
            BizReceiptInspectItem inspectItem = inspectItemDataWrap.getById(itemDTO.getPreReceiptItemId());
            // 不合格数量
            paymentPlanItemDTO.setUnqualifiedQty(inspectItem == null ? BigDecimal.ZERO : inspectItem.getUnqualifiedQty());
            // 未到货数量
            paymentPlanItemDTO.setUnarrivalQty(inspectItem == null ? BigDecimal.ZERO : inspectItem.getUnarrivalQty());
            // 物资入库单号
            paymentPlanItemDTO.setInputReceiptCode(itemDTO.getReceiptCode());
            paymentPlanItemDTO.setInputReceiptId(itemDTO.getHeadId());
            paymentPlanItemDTO.setInputReceiptType(EnumReceiptType.STOCK_INPUT_INSPECT.getValue());
            // 入库数量
            paymentPlanItemDTO.setInputQty(itemDTO.getQty());
            paymentPlanItemDTO.setPreReceiptType(itemDTO.getReceiptType());
            paymentPlanItemDTO.setPreReceiptItemId(itemDTO.getId());
            paymentPlanItemDTO.setPreReceiptHeadId(itemDTO.getHeadId());
            paymentPlanItemDTOS.add(paymentPlanItemDTO);

            if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
                totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
            }
        }

        paymentPlanHeadDTO.setQty(totalAmount.multiply(paymentNode.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(paymentPlanHeadDTO);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(paymentPlanItemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : paymentPlanItemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(paymentPlanHeadDTO.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }


    public boolean isSameDay(Date date1, Date date2) {
        if (date1 == null || date2 == null) {
            return false;
        }
        LocalDate localDate1 = date1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate localDate2 = date2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate1.isEqual(localDate2);
    }

    /**
     * 定时任务每天检查是否需要生成到货款的付款计划
     * 离岸：涉及两种合同的付款计划
     * 1、华信对能殷：定时任务：单据提交时间+N天（根据送货单号关联其他合同号）
     * 2、能殷对子供应商：定时任务：子合同付款节点（若存在到货款）则就提交之后+N天
     * 内贸：涉及两种合同的付款计划
     * 1、华信对代理商：定时任务：单据提交时间+N天（根据送货单号关联其他合同号）
     * 2、能殷对子供应商：定时任务：子合同付款节点（若存在到货款）则就提交之后+N天
     */
    @Transactional(rollbackFor = Exception.class)
    public void genNode4PaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }

        // 查找有到货款的付款节点
        List<BizReceiptContractPaymentNode> paymentNodes = bizReceiptContractPaymentNodeDataWrap.list(
                new QueryWrapper<BizReceiptContractPaymentNode>().lambda()
                        .eq(BizReceiptContractPaymentNode::getPaymentNode, EnumContractPaymentNode.NODE_4.getCode())
        );
        if (UtilCollection.isEmpty(paymentNodes)) {
            return;
        }

        Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap = paymentNodes.stream()
                .collect(Collectors.groupingBy(BizReceiptContractPaymentNode::getHeadId));
        List<Long> contractIds = paymentNodes.stream()
                .map(BizReceiptContractPaymentNode::getHeadId)
                .collect(Collectors.toList());

        List<Integer> sendTypeList = Arrays.asList(
                EnumSendType.OFFSHORE_PROCUREMENT.getValue(),
                EnumSendType.INLAND_PROCUREMENT.getValue(),
                EnumSendType.ONSHORE_PROCUREMENT.getValue()
        );

        List<BizReceiptContractHead> bizReceiptContractHeadList = bizReceiptContractHeadDataWrap.listByIds(contractIds);
        if (UtilCollection.isEmpty(bizReceiptContractHeadList)) return;

        Map<String, BizReceiptContractHead> contractHeadMap = bizReceiptContractHeadList.stream()
                .collect(Collectors.toMap(BizReceiptContractHead::getReceiptCode, Function.identity()));

        List<BizReceiptDeliveryNoticeHead> deliveryNoticeHeads = bizReceiptDeliveryNoticeHeadDataWrap.list(
                new QueryWrapper<BizReceiptDeliveryNoticeHead>().lambda()
                        .in(BizReceiptDeliveryNoticeHead::getSendType, sendTypeList)
                        .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptDeliveryNoticeHead::getReceiptCode, receiptCodeList)
                        .eq(BizReceiptDeliveryNoticeHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue())
        );

        List<BizReceiptDeliveryNoticeHeadDTO> deliveryNoticeHeadDTOS = UtilCollection.toList(deliveryNoticeHeads, BizReceiptDeliveryNoticeHeadDTO.class);
        dataFillService.fillAttr(deliveryNoticeHeadDTOS);

        List<BizReceiptDeliveryNoticeItemDTO> deliveryNoticeItemDTOList = new ArrayList<>();
        for (BizReceiptDeliveryNoticeHeadDTO headDTO : deliveryNoticeHeadDTOS) {
            List<BizReceiptDeliveryNoticeItemDTO> filteredItems = headDTO.getItemList().stream()
                    .filter(item -> contractHeadMap.containsKey(item.getContractCode()))
                    .collect(Collectors.toList());
            filteredItems.forEach(c -> c.setSendType(headDTO.getSendType()));
            deliveryNoticeItemDTOList.addAll(filteredItems);
        }

        // 按送货单分组而不是按合同分组，确保每个送货单生成独立的付款计划
        Map<Long, List<BizReceiptDeliveryNoticeItemDTO>> deliveryHeadMap = deliveryNoticeItemDTOList.stream()
                .collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getHeadId));

        deliveryHeadMap.forEach((deliveryHeadId, items) -> {
            // 按合同分组（一个送货单可能包含多个合同的物料）
            Map<String, List<BizReceiptDeliveryNoticeItemDTO>> contractCodeMap = items.stream()
                    .collect(Collectors.groupingBy(BizReceiptDeliveryNoticeItemDTO::getContractCode));
            
            contractCodeMap.forEach((contractCode, contractItems) -> {
                BizReceiptContractHead contractHead = contractHeadMap.get(contractCode);
                if (contractHead == null) {
                    return;
                }

                BizReceiptContractHeadDTO contractHeadDTO = UtilBean.newInstance(contractHead, BizReceiptContractHeadDTO.class);
                dataFillService.fillAttr(contractHeadDTO);
                List<BizReceiptContractPaymentNode> nodes = payNodeMap.get(contractHead.getId());

                BizReceiptContractPaymentNode paymentNode = nodes.get(0);
                Integer paymentDay = paymentNode.getPaymentCondition();
                List<BizReceiptDeliveryNoticeItemDTO> validItems = Collections.emptyList();
                
                // 离岸、内贸实际离港日期+天数自动生成付款计划
                if (contractItems.get(0).getSendType().equals(EnumSendType.OFFSHORE_PROCUREMENT.getValue()) 
                        || contractItems.get(0).getSendType().equals(EnumSendType.INLAND_PROCUREMENT.getValue())) {
                    BizReceiptDeliveryWaybillHead waybillHead = bizReceiptDeliveryWaybillHeadDataWrap.getOne(new QueryWrapper<BizReceiptDeliveryWaybillHead>().lambda()
                            .eq(BizReceiptDeliveryWaybillHead::getDeliveryNoticeReceiptHeadId, deliveryHeadId));
                    if (waybillHead != null && waybillHead.getActualDepartureTime() != null) {
                        validItems = contractItems.stream()
                                .filter(c -> isSameDay(UtilDate.plusDays(waybillHead.getActualDepartureTime(), paymentDay), new Date()))
                                .collect(Collectors.toList());
                    }
                } else {
                    validItems = contractItems.stream()
                            .filter(c -> isSameDay(UtilDate.plusDays(c.getModifyTime(), paymentDay), new Date()))
                            .collect(Collectors.toList());
                }

                if (UtilCollection.isNotEmpty(validItems)) {
                    this.generatePaymentPlanForDelivery(contractHeadDTO, paymentNode, validItems);
                }
            });
        });
        // 带有送货单号的合同
        List<BizReceiptContractHead> matchedContracts = bizReceiptContractHeadList.stream()
                .filter(c -> c.getDeliveryCode() != null && c.getDeliveryCode().length() > 1)
                .filter(c -> {
                    List<BizReceiptContractPaymentNode> nodes = payNodeMap.get(c.getId());
                    if (UtilCollection.isEmpty(nodes)) {
                        return false;
                    }
                    Integer paymentDay = nodes.get(0).getPaymentCondition();
                    return isSameDay(UtilDate.plusDays(c.getModifyTime(), paymentDay), new Date());
                })
                .collect(Collectors.toList());

        List<BizReceiptContractHeadDTO> contractHeadDTOS = UtilCollection.toList(matchedContracts, BizReceiptContractHeadDTO.class);
        dataFillService.fillAttr(contractHeadDTOS);
        for (BizReceiptContractHeadDTO contractDTO : contractHeadDTOS) {
            generateAndSavePaymentPlan(contractDTO, payNodeMap);
        }
    }

    private void generateAndSavePaymentPlan(BizReceiptContractHeadDTO contractDTO, Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap) {
        List<BizReceiptContractPaymentNode> nodes = payNodeMap.get(contractDTO.getId());
        if (UtilCollection.isEmpty(nodes)) return;

        BizReceiptContractPaymentNode paymentNode = nodes.get(0);
        BizReceiptPaymentPlanHeadDTO planHead = new BizReceiptPaymentPlanHeadDTO();
        planHead.setId(null);
        planHead.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        planHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        planHead.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        planHead.setCreateUserId(1L);

        planHead.setPlanType(this.getPlanTypeByPurchaseType(contractDTO.getPurchaseType()));

        planHead.setContractId(contractDTO.getId());
        planHead.setPaymentMonth(getMonth());
        planHead.setPaymentNode(paymentNode.getPaymentNode());
        planHead.setRate(paymentNode.getRate());

        if (UtilCollection.isNotEmpty(contractDTO.getItemList())) {
            planHead.setTaxCodeRate(contractDTO.getItemList().get(0).getTaxCodeRate());
            planHead.setContractAmount(contractDTO.getItemList().stream()
                    .map(BizReceiptContractItemDTO::getTaxAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        bizReceiptPaymentPlanHeadDataWrap.saveDto(planHead);

        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BizReceiptPaymentPlanItemDTO> itemDTOS = new ArrayList<>();

        for (BizReceiptContractItemDTO itemDTO : contractDTO.getItemList()) {
            BizReceiptPaymentPlanItemDTO item = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, item);
            item.setId(null);
            item.setHeadId(planHead.getId());
            item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            item.setQty(itemDTO.getQty());
            item.setCreateUserId(1L);
            item.setPurchaseReceiptCode(itemDTO.getPurchaseReceiptCode());
            item.setPurchaseReceiptRid(itemDTO.getPurchaseReceiptRid());
            item.setPreReceiptType(contractDTO.getReceiptType());
            item.setPreReceiptItemId(itemDTO.getId());
            item.setPreReceiptHeadId(itemDTO.getHeadId());
            itemDTOS.add(item);

            if (itemDTO.getQty() != null && itemDTO.getTaxPrice() != null) {
                totalAmount = totalAmount.add(itemDTO.getQty().multiply(itemDTO.getTaxPrice()));
            }
        }

        planHead.setQty(totalAmount.multiply(paymentNode.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        bizReceiptPaymentPlanHeadDataWrap.updateDtoById(planHead);
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(itemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : itemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(planHead.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 定时任务每天检查是否需要生成工程服务结算款的付款计划
     */
    @Transactional(rollbackFor = Exception.class)
    public void genNode8PaymentPlan(String receiptCodes) {
        List<String> receiptCodeList = new ArrayList<>();
        if (UtilString.isNotNullOrEmpty(receiptCodes)) {
            receiptCodeList = Arrays.asList(receiptCodes.split(";"));
        }

        // 查找有工程服务结算款的付款节点
        List<BizReceiptContractPaymentNode> paymentNodes = bizReceiptContractPaymentNodeDataWrap.list(
                new QueryWrapper<BizReceiptContractPaymentNode>().lambda()
                        .eq(BizReceiptContractPaymentNode::getPaymentNode, EnumContractPaymentNode.NODE_8.getCode())
        );
        if (UtilCollection.isEmpty(paymentNodes)) {
            return;
        }

        Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap = paymentNodes.stream()
                .collect(Collectors.groupingBy(BizReceiptContractPaymentNode::getHeadId));
        List<Long> contractIds = paymentNodes.stream()
                .map(BizReceiptContractPaymentNode::getHeadId)
                .collect(Collectors.toList());
        // 非生产，服务类，施工类合同(已完成或已终止)且未生成过工程服务结算款
        List<BizReceiptContractHead> bizReceiptContractHeadList = bizReceiptContractHeadDataWrap.list(new QueryWrapper<BizReceiptContractHead>().lambda()
                .in(BizReceiptContractHead::getId, contractIds)
                .eq(BizReceiptContractHead::getContractSubType, 2)
                .eq(BizReceiptContractHead::getIsSettlement, 0)
                .in(UtilCollection.isNotEmpty(receiptCodeList), BizReceiptContractHead::getReceiptCode, receiptCodeList)
                .in(BizReceiptContractHead::getReceiptStatus, Arrays.asList(EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue(), EnumReceiptStatus.RECEIPT_STATUS_TERMINATED.getValue())));

        if (UtilCollection.isEmpty(bizReceiptContractHeadList)) return;
        List<BizReceiptContractHeadDTO> contractHeadDTOList = UtilCollection.toList(bizReceiptContractHeadList, BizReceiptContractHeadDTO.class);
        dataFillService.fillAttr(contractHeadDTOList);
        for (BizReceiptContractHeadDTO contractHeadDTO : contractHeadDTOList) {
            // 已完成产值
            BigDecimal totalValue = contractHeadDTO.getSubItemList().stream().map(BizReceiptContractSubItem::getTotalValue).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (BigDecimal.ZERO.compareTo(totalValue) == 0) {
                continue;
            }
            this.genNode8PaymentPlan(contractHeadDTO, payNodeMap, totalValue);
            contractHeadDTO.setIsSettlement(1);
            bizReceiptContractHeadDataWrap.updateDtoById(contractHeadDTO);
        }

    }


    private void genNode8PaymentPlan(BizReceiptContractHeadDTO contractHeadDTO, Map<Long, List<BizReceiptContractPaymentNode>> payNodeMap, BigDecimal totalValue) {
        BizReceiptContractPaymentNode paymentNode = payNodeMap.get(contractHeadDTO.getId()).get(0);
        BizReceiptPaymentPlanHeadDTO planHead = new BizReceiptPaymentPlanHeadDTO();
        planHead.setId(null);
        planHead.setReceiptType(EnumReceiptType.PAYMENT_PLAN.getValue());
        planHead.setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
        planHead.setReceiptCode(bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_PLAN.getValue()));
        planHead.setCreateUserId(1L);

        planHead.setPlanType(this.getPlanTypeByPurchaseType(contractHeadDTO.getPurchaseType()));

        planHead.setContractId(contractHeadDTO.getId());
        planHead.setPaymentMonth(getMonth());
        planHead.setPaymentNode(paymentNode.getPaymentNode());
        planHead.setRate(paymentNode.getRate());

        planHead.setQty(totalValue.multiply(paymentNode.getRate()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        planHead.setTaxCodeRate(contractHeadDTO.getItemList().get(0).getTaxCodeRate());
        planHead.setContractAmount(contractHeadDTO.getItemList().stream()
                .map(BizReceiptContractItemDTO::getTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));


        bizReceiptPaymentPlanHeadDataWrap.saveDto(planHead);

        List<BizReceiptPaymentPlanItemDTO> itemDTOS = new ArrayList<>();

        for (BizReceiptContractItemDTO itemDTO : contractHeadDTO.getItemList()) {
            BizReceiptPaymentPlanItemDTO item = new BizReceiptPaymentPlanItemDTO();
            UtilBean.copy(itemDTO, item);
            item.setId(null);
            item.setHeadId(planHead.getId());
            item.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue());
            item.setQty(itemDTO.getQty());
            item.setCreateUserId(1L);
            item.setPreReceiptType(contractHeadDTO.getReceiptType());
            item.setPreReceiptItemId(itemDTO.getId());
            item.setPreReceiptHeadId(itemDTO.getHeadId());
            itemDTOS.add(item);
        }
        bizReceiptPaymentPlanItemDataWrap.saveBatchDto(itemDTOS);
        // 保存单据流
        List<BizCommonReceiptRelation> dtoList = new ArrayList<>();
        for (BizReceiptPaymentPlanItemDTO item : itemDTOS) {
            BizCommonReceiptRelation dto = new BizCommonReceiptRelation().setReceiptType(planHead.getReceiptType())
                    .setReceiptHeadId(item.getHeadId()).setReceiptItemId(item.getId())
                    .setPreReceiptType(item.getPreReceiptType()).setPreReceiptHeadId(item.getPreReceiptHeadId())
                    .setPreReceiptItemId(item.getPreReceiptItemId());
            dtoList.add(dto);
        }
        if (UtilCollection.isNotEmpty(dtoList)) {
            receiptRelationService.multiSaveReceiptTree(dtoList);
        }
    }

    /**
     * 删除
     *
     * @param ctx 入参上下文
     */
    public void remove(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptPaymentPlanHead head = bizReceiptPaymentPlanHeadDataWrap.getById(headId);
        if (UtilObject.isNull(head)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_NOT_EXIST);
        }
        if (!EnumReceiptStatus.RECEIPT_STATUS_UN_COMPILE.getValue().equals(head.getReceiptStatus())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_RECEIPT_STATUS_FALSE);
        }
        // 属性填充
        BizReceiptPaymentPlanHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptPaymentPlanHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        // 删除单据
        bizReceiptPaymentPlanHeadDataWrap.removeById(headId);
        bizReceiptPaymentPlanItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptPaymentPlanItem>().eq(BizReceiptPaymentPlanItem::getHeadId, headId));
        bizReceiptContractSubItemDataWrap.remove(new LambdaQueryWrapper<BizReceiptContractSubItem>().eq(BizReceiptContractSubItem::getHeadId, headId));
        // 设置操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_DELETE);
        // 设置上下文PO
        ctx.setPoContextData(headDTO);
    }

}
