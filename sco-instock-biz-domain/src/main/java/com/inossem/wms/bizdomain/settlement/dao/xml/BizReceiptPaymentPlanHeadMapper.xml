<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.inossem.wms.bizdomain.settlement.dao.BizReceiptPaymentPlanHeadMapper">

    <select id="selectPageVo" resultType="com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentPlanPageVO">
        SELECT
            biz_receipt_payment_plan_head.id,
            biz_receipt_payment_plan_head.receipt_code,
            biz_receipt_contract_head.receipt_code contractCode,
            biz_receipt_contract_head.contract_name ,
            biz_receipt_contract_head.purchase_type,
            biz_receipt_contract_head.first_party,
            biz_receipt_payment_plan_head.plan_type,
            dic_supplier.supplier_name,
            biz_receipt_payment_plan_head.payment_node,
            biz_receipt_contract_head.currency,
            contract_create_user.user_name contract_create_user_name,
            biz_receipt_payment_plan_head.payment_month,
            biz_receipt_payment_plan_head.qty,
            biz_receipt_payment_plan_head.receipt_status,
            biz_receipt_payment_plan_head.create_time,
            biz_receipt_delivery_notice_head.batch_code send_batch,
            sys_user.user_name createUserName
        FROM
            biz_receipt_payment_plan_head
            left join biz_receipt_payment_plan_item on biz_receipt_payment_plan_head.id = biz_receipt_payment_plan_item.head_id
            left join biz_receipt_delivery_notice_head ON (biz_receipt_delivery_notice_head.id = biz_receipt_payment_plan_item.pre_receipt_head_id) or (biz_receipt_delivery_notice_head.purchase_code = biz_receipt_payment_plan_item.purchase_receipt_code AND biz_receipt_payment_plan_item.purchase_receipt_code != '' AND biz_receipt_delivery_notice_head.batch_code != '')
            left join biz_receipt_contract_head  on biz_receipt_payment_plan_head.contract_id = biz_receipt_contract_head.id
            left join sys_user  on biz_receipt_payment_plan_head.create_user_id = sys_user.id
            left join dic_supplier on biz_receipt_contract_head.supplier_id = dic_supplier.id
            left join sys_user contract_create_user on biz_receipt_contract_head.create_user_id = contract_create_user.id
            ${ew.customSqlSegment}
        group by biz_receipt_payment_plan_head.id
        ORDER BY
            CASE biz_receipt_payment_plan_head.receipt_status
            WHEN 209 THEN 1
            WHEN 210 THEN 2
            WHEN 93  THEN 3
            WHEN 90  THEN 4
        END ASC,
            CASE biz_receipt_contract_head.purchase_type
            WHEN 7 THEN 1
            WHEN 3 THEN 2
            WHEN 4 THEN 3
            WHEN 2 THEN 4
            WHEN 1 THEN 5
            WHEN 5 THEN 6
            END ASC
    </select>

    <select id="listByFirstParty" resultType="com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead">
        SELECT biz_receipt_payment_plan_head.*
        FROM biz_receipt_payment_plan_head
                 join biz_receipt_contract_head on biz_receipt_payment_plan_head.contract_id = biz_receipt_contract_head.id and biz_receipt_contract_head.is_delete = 0
        WHERE biz_receipt_payment_plan_head.is_delete = 0
          and biz_receipt_payment_plan_head.receipt_type = 201
          and biz_receipt_contract_head.first_party = #{firstParty}
          and biz_receipt_payment_plan_head.receipt_status = #{receiptStatus}
    </select>

</mapper>
