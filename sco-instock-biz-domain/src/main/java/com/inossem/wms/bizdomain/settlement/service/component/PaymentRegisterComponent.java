package com.inossem.wms.bizdomain.settlement.service.component;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.inossem.wms.bizbasis.common.service.biz.BizCommonService;
import com.inossem.wms.bizbasis.common.service.biz.DataFillService;
import com.inossem.wms.bizbasis.common.service.biz.DictionaryService;
import com.inossem.wms.bizbasis.common.service.biz.I18nTextCommonService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptAttachmentService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptOperationLogService;
import com.inossem.wms.bizbasis.common.service.biz.ReceiptRelationService;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDataWrap;
import com.inossem.wms.bizbasis.masterdata.user.service.datawrap.SysUserDeptOfficeRelDataWrap;
import com.inossem.wms.bizbasis.sap.restful.service.HXOaIntegerfaceService;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractSubItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentPlanHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentRegisterHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentRegisterItemDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentSettlementHeadDataWrap;
import com.inossem.wms.bizdomain.settlement.service.datawrap.BizReceiptPaymentSettlementItemDataWrap;
import com.inossem.wms.bizdomain.srm.service.SrmService;
import com.inossem.wms.bizdomain.supplier.service.datawrap.DicSupplierDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumRealYn;
import com.inossem.wms.common.enums.EnumReceiptOperationType;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.EnumSequenceCode;
import com.inossem.wms.common.enums.contract.EnumContractFirstParty;
import com.inossem.wms.common.enums.dept.EnumDept;
import com.inossem.wms.common.enums.workflow.EnumApprovalLevel;
import com.inossem.wms.common.enums.workflow.EnumApprovalStatus;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.approval.dto.BizApprovalReceiptInstanceRelDTO;
import com.inossem.wms.common.model.approval.dto.BizApproveRecordDTO;
import com.inossem.wms.common.model.approval.dto.RevokeDTO;
import com.inossem.wms.common.model.auth.user.entity.SysUser;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.contract.dto.BizReceiptContractSubItemDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractSubItem;
import com.inossem.wms.common.model.bizdomain.inconformity.dto.BizReceiptInconformityHeadDTO;
import com.inossem.wms.common.model.bizdomain.register.dto.BizReceiptRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentInvoiceDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentRegisterHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentRegisterItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementHeadDTO;
import com.inossem.wms.common.model.bizdomain.settlement.dto.BizReceiptPaymentSettlementItemDTO;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentPlanHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentRegisterHead;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentRegisterItem;
import com.inossem.wms.common.model.bizdomain.settlement.entity.BizReceiptPaymentSettlementHead;
import com.inossem.wms.common.model.bizdomain.settlement.po.BizReceiptPaymentSettlementSearchPO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentRegisterPageVO;
import com.inossem.wms.common.model.bizdomain.settlement.vo.BizReceiptPaymentSettlementVO;
import com.inossem.wms.common.model.common.ExtendVO;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.ButtonVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.masterdata.supplier.entity.DicSupplier;
import com.inossem.wms.common.mybatisplus.WmsLambdaQueryWrapper;
import com.inossem.wms.common.util.UtilBean;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilConst;
import com.inossem.wms.common.util.UtilCurrentContext;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.UtilString;
import com.inossem.wms.system.workflow.service.business.biz.ApprovalService;
import com.inossem.wms.system.workflow.service.business.biz.WorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29
 */
@Component
@Slf4j
public class PaymentRegisterComponent {

    @Autowired
    private BizReceiptPaymentRegisterHeadDataWrap bizReceiptPaymentRegisterHeadDataWrap;
    @Autowired
    private BizReceiptPaymentRegisterItemDataWrap bizReceiptPaymentRegisterItemDataWrap;
    @Autowired
    private DataFillService dataFillService;
    @Autowired
    private BizCommonService bizCommonService;
    @Autowired
    private ReceiptAttachmentService receiptAttachmentService;
    @Autowired
    private ReceiptOperationLogService receiptOperationLogService;
    @Autowired
    private BizReceiptPaymentSettlementHeadDataWrap bizReceiptPaymentSettlementHeadDataWrap;
    @Autowired
    private BizReceiptPaymentPlanHeadDataWrap bizReceiptPaymentPlanHeadDataWrap;
    @Autowired
    private BizReceiptPaymentSettlementItemDataWrap bizReceiptPaymentSettlementItemDataWrap;
    @Autowired
    private BizReceiptContractSubItemDataWrap bizReceiptContractSubItemDataWrap;
    @Autowired
    private I18nTextCommonService i18nTextCommonService;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private HXOaIntegerfaceService hXOaIntegerfaceService;
    @Autowired
    private SysUserDeptOfficeRelDataWrap sysUserDeptOfficeRelDataWrap;
    @Autowired
    private SysUserDataWrap sysUserDataWrap;
    @Autowired
    private SrmService srmService;
    @Autowired
    private DictionaryService dictionaryService;
    @Autowired
    private ReceiptRelationService receiptRelationService;
    @Autowired
    private DicSupplierDataWrap dicSupplierDataWrap;
    @Autowired
    private BizReceiptContractHeadDataWrap bizReceiptContractHeadDataWrap;
    @Autowired
    private HistoryService historyServiceImpl;
    @Value("${wms.srm.createPaymentPlan-url}")
    private String createPaymentPlanUrl;

    /**
     * 页面初始化:
     */
    public void setInit(BizContext ctx) {
        // 页面初始化设置
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> resultVO = new BizResultVO<>(
                new BizReceiptPaymentRegisterHeadDTO().setReceiptType(EnumReceiptType.PAYMENT_REGISTER.getValue())
                        .setReceiptStatus(EnumReceiptStatus.RECEIPT_STATUS_CREATE.getValue())
                        .setCreateTime(UtilDate.getNow()).setCreateUserName(ctx.getCurrentUser().getUserName()),
                new ExtendVO(), new ButtonVO().setButtonSave(true).setButtonSubmit(true));
        // 设置页面初始化数据到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 开启附件
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启附件")}
     */
    public void setExtendAttachment(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置附件开启/关闭
        resultVO.getExtend().setAttachmentRequired(UtilConst.getInstance().isAttachmentRequired());
    }

    /**
     * 开启操作日志
     *
     * @in ctx 入参 {@link BizResultVO ("extend":"扩展功能")}
     * @out ctx 出参 {@link BizResultVO ("extend":"扩展功能开启操作日志")}
     */
    public void setExtendOperationLog(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 设置操作日志开启/关闭
        resultVO.getExtend().setOperationLogRequired(UtilConst.getInstance().isOperationLogRequired());
    }

    /**
     * 设置详情页单据流
     *
     * @param ctx 上下文
     */
    public void setInfoExtendRelation(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        resultVO.getExtend().setRelationRequired(true);
        if (UtilObject.isNotNull(resultVO.getHead())) {
            resultVO.getHead().setRelationList(receiptRelationService.getReceiptTree(resultVO.getHead().getReceiptType(), resultVO.getHead().getId(), null));
        }
        // 设置单据流详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 分页查询付款登记
     */
    public void getPageVo(BizContext ctx) {
        // 上下文入参
        BizReceiptPaymentSettlementSearchPO po = ctx.getPoContextData();
        if (UtilObject.isNotNull(po.getCreateTimeEnd())) {
            po.setCreateTimeEnd(UtilDate.getEndOfDay(po.getCreateTimeEnd()));
        }
        // 分页处理
        IPage<BizReceiptPaymentRegisterPageVO> page = po.isPaging() ? po.getPageObj(BizReceiptPaymentRegisterPageVO.class) : null;
        // 分页列表查询
        List<BizReceiptPaymentRegisterPageVO> resultList = bizReceiptPaymentRegisterHeadDataWrap.selectPageVo(page, new WmsLambdaQueryWrapper<BizReceiptPaymentSettlementSearchPO>()
                .eq(true, BizReceiptPaymentSettlementSearchPO::getIsDelete, BizReceiptPaymentRegisterHead.class, EnumRealYn.FALSE.getIntValue())
                .eq(UtilString.isNotNullOrEmpty(po.getReceiptCode()), BizReceiptPaymentSettlementSearchPO::getReceiptCode, BizReceiptPaymentRegisterHead.class, po.getReceiptCode())
                .in(UtilCollection.isNotEmpty(po.getReceiptStatusList()), BizReceiptPaymentSettlementSearchPO::getReceiptStatus, BizReceiptPaymentRegisterHead.class, po.getReceiptStatusList())
                .like(UtilString.isNotNullOrEmpty(po.getContractCode()), BizReceiptPaymentSettlementSearchPO::getReceiptCode, BizReceiptContractHead.class, po.getContractCode())
                .like(UtilString.isNotNullOrEmpty(po.getContractName()), BizReceiptPaymentSettlementSearchPO::getContractName, BizReceiptContractHead.class, po.getContractName())
                .eq(UtilNumber.isNotEmpty(po.getPurchaseType()), BizReceiptPaymentSettlementSearchPO::getPurchaseType, BizReceiptContractHead.class, po.getPurchaseType())
                .eq(UtilString.isNotNullOrEmpty(po.getPaymentMonth()), BizReceiptPaymentSettlementSearchPO::getPaymentMonth, BizReceiptPaymentSettlementHead.class, po.getPaymentMonth())
                .like(UtilString.isNotNullOrEmpty(po.getSettlementCode()), BizReceiptPaymentSettlementSearchPO::getReceiptCode, BizReceiptPaymentSettlementHead.class, po.getSettlementCode())
                .like(UtilString.isNotNullOrEmpty(po.getCreateUserName()), BizReceiptPaymentSettlementSearchPO::getUserName, SysUser.class, po.getCreateUserName())
                .between(UtilObject.isNotNull(po.getCreateTimeStart()) && UtilObject.isNotNull(po.getCreateTimeEnd()), BizReceiptPaymentSettlementSearchPO::getCreateTime, BizReceiptPaymentRegisterHead.class, po.getCreateTimeStart(), po.getCreateTimeEnd())
        );
        // 设置返回信息到上下文
        ctx.setVoContextData(new PageObjectVO<>(resultList, po.isPaging() ? Objects.requireNonNull(page).getTotal() : resultList.size()));
    }

    public void getInfo(BizContext ctx) {
        // 入参上下文
        Long headId = ctx.getContextData(Const.BIZ_CONTEXT_KEY_ID);
        if (UtilNumber.isEmpty(headId)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 获取详情
        BizReceiptPaymentRegisterHead head = bizReceiptPaymentRegisterHeadDataWrap.getById(headId);
        // 转DTO
        BizReceiptPaymentRegisterHeadDTO headDTO =
                UtilBean.newInstance(head, BizReceiptPaymentRegisterHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTO);
        List<BizReceiptPaymentSettlementHead> paymentSettlementHeadList = bizReceiptPaymentSettlementHeadDataWrap.listByIds(headDTO.getItemList().stream().map(e->e.getSettlementHeadId()).collect(
            Collectors.toList()));
        List<BizReceiptPaymentSettlementHeadDTO> paymentSettlementHeadDTOList =
                UtilCollection.toList(paymentSettlementHeadList, BizReceiptPaymentSettlementHeadDTO.class);
        dataFillService.fillAttr(paymentSettlementHeadDTOList);

        //headDTO.setPaymentSettlementHead(paymentSettlementHead);
        headDTO.setInvoiceList(paymentSettlementHeadDTOList.stream().map(BizReceiptPaymentSettlementHeadDTO::getInvoiceList).filter(UtilCollection::isNotEmpty).flatMap(List::stream).collect(Collectors.toList()));
       // headDTO.setProgressList(paymentSettlementHeadDTO.getProgressList());
       // headDTO.setLackMatVOList(paymentSettlementHeadDTO.getLackMatVOList());

        // 非生产物资类、服务类、施工类,查询分项信息
//        List<Integer> purchaseTypeList = Arrays.asList(EnumPurchaseType.NON_PRODUCTION_MATERIAL.getCode(), EnumPurchaseType.SERVICE.getCode(), EnumPurchaseType.CONSTRUCTION.getCode());
//        if (paymentSettlementHeadDTO.getSettlementType().equals(1) && purchaseTypeList.contains(headDTO.getPurchaseType())) {
//            ctx.setPoContextData(paymentSettlementHeadDTO.getItemList());
//            this.selectSubItem(ctx);
//            List<BizReceiptContractSubItemDTO> subItemDTOList = ctx.getVoContextData();
//            headDTO.setSubItemList(subItemDTOList);
//        }
        // ctx.setPoContextData(headDTO);
        // this.fillData(ctx);
        // 设置按钮组权限
        ButtonVO buttonVO = this.setButton(headDTO);
        // 设置审批按钮权限
        workflowService.setApproveButton(buttonVO, ctx.getContextData("taskId"));
        // 设置详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, new BizResultVO<>(headDTO, new ExtendVO(), buttonVO));
    }

    /**
     * 开启审批
     *
     * @param ctx 入参上下文
     */
    public void setExtendWf(BizContext ctx) {
        // 上下文入参
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> resultVO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        // 需要审批
        boolean wfByReceiptType = true;

        if (UtilObject.isNull(resultVO.getHead())) {
            // 初始化 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
        } else {
            // 详情页 - 设置审批开启/关闭
            resultVO.getExtend().setWfRequired(wfByReceiptType);
            if (UtilObject.isNotNull(resultVO.getHead())) {
                List<BizApproveRecordDTO> approveList = approvalService.getHistoryActivity(resultVO.getHead().getReceiptCode());
                resultVO.getHead().setApproveList(approveList);
                if (UtilCollection.isNotEmpty(approveList)) {
                    resultVO.getHead().setProInstanceId(Long.valueOf(approveList.get(approveList.size() - 1).getProInstanceId()));
                }
            }
        }
        // 设置审批详情到上下文
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, resultVO);
    }

    /**
     * 查询分项信息
     * 数量合并后
     *
     * @param ctx ctx
     */
    public void selectSubItem(BizContext ctx) {

        List<BizReceiptPaymentSettlementItemDTO> po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        List<BizReceiptContractSubItemDTO> subItemDTOList = new ArrayList<>();
        for (BizReceiptPaymentSettlementItemDTO itemDTO : po) {
            List<BizReceiptContractSubItem> list = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda().eq
                    (BizReceiptContractSubItem::getPaymentPlanId, itemDTO.getPaymentPlanHeadDTO().getId()));
            Map<String, List<BizReceiptContractSubItem>> map = list.stream().collect(Collectors.groupingBy(BizReceiptContractSubItem::getSubItemName));

            map.forEach((k, v) -> {
                BizReceiptContractSubItemDTO subItemDTO = new BizReceiptContractSubItemDTO();
                UtilBean.copy(v.get(0), subItemDTO);
                // 数量取合并后的
                subItemDTO.setPaymentPlanQty(v.stream().map(BizReceiptContractSubItem::getPaymentPlanQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTO.setPaymentPlanValue(v.stream().map(BizReceiptContractSubItem::getPaymentPlanValue).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTOList.add(subItemDTO);
            });

        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, subItemDTOList);

    }

    /**
     * 按钮组
     *
     * @param headDTO 付款结算单
     * @return 按钮组对象
     */
    private ButtonVO setButton(BizReceiptPaymentRegisterHeadDTO headDTO) {
        // 单据抬头状态
        Integer receiptStatus = headDTO.getReceiptStatus();
        if (UtilObject.isNull(receiptStatus)) {
            return new ButtonVO();
        }
        ButtonVO buttonVO = new ButtonVO();
        if (EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue().equals(receiptStatus)) {
            // 草稿 -【保存、提交】
            return buttonVO.setButtonSave(true).setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue().equals(receiptStatus)) {
            // 已驳回 -【提交】
            return buttonVO.setButtonSubmit(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(receiptStatus)) {
            // 审批中 -【撤销】
            return buttonVO.setButtonRevoke(true);
        }
        if (EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue().equals(receiptStatus)) {
            // 待处理 -【提交、撤销】
            return buttonVO.setButtonSubmit(true).setButtonRevoke(true);
        }
        return buttonVO;
    }

    /**
     * 保存-校验入参
     */
    public void checkSaveData(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 抬头数据是否为空
        if (po == null) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_LOST);
        }
        // 行项目数据是否为空
        if (UtilCollection.isEmpty(po.getItemList())) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_EMPTY_ITEM);
        }
    }


    /**
     * 保存单据
     *
     * @param ctx ctx
     */
    public void save(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型(为空则是保存按钮操作)
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前用户信息
        CurrentUser user = ctx.getCurrentUser();
        /* ********************** head处理开始 *************************/
        String code = po.getReceiptCode();
        if (UtilNumber.isEmpty(po.getId())) {
            po.setCreateUserId(user.getId());
        }
        po.setModifyUserId(user.getId());
        po.setReceiptType(EnumReceiptType.PAYMENT_REGISTER.getValue());
        po.setReceiptStatus(UtilNumber.isEmpty(po.getReceiptStatus()) ? EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue() : po.getReceiptStatus());
        // 验证是否为新增
        if (UtilNumber.isNotEmpty(po.getId())) {
            bizReceiptPaymentRegisterHeadDataWrap.updateDtoById(po);
            // 修改前删除item
            this.deleteItem(po);

            if (null == operationLogType) {
                // 设置上下文单据日志 - 修改
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_SAVE);
            }
        } else {
            code = bizCommonService.getNextSequenceValueDaily(EnumSequenceCode.PAYMENT_REGISTER.getValue());
            po.setReceiptCode(code);
            po.setId(null);
            bizReceiptPaymentRegisterHeadDataWrap.saveDto(po);
            if (null == operationLogType) {
                // 设置上下文单据日志 - 创建
                ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE,
                        EnumReceiptOperationType.RECEIPT_OPERATION_ESTABLISH);
            }
        }

        AtomicInteger rid = new AtomicInteger(1);
        for (BizReceiptPaymentRegisterItemDTO itemDto : po.getItemList()) {
            itemDto.setId(null);
            itemDto.setHeadId(po.getId());
            itemDto.setRid(Integer.toString(rid.getAndIncrement()));
            itemDto.setItemStatus(EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
            itemDto.setCreateUserId(user.getId());
        }
        bizReceiptPaymentRegisterItemDataWrap.saveBatchDto(po.getItemList());

        /* ********************** item处理结束 *************************/

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, code);
    }


    private void deleteItem(BizReceiptPaymentRegisterHeadDTO headDTO) {
        UpdateWrapper<BizReceiptPaymentRegisterItem> wrapper = new UpdateWrapper<>();
        wrapper.lambda().eq(UtilNumber.isNotEmpty(headDTO.getId()), BizReceiptPaymentRegisterItem::getHeadId,
                headDTO.getId());
        bizReceiptPaymentRegisterItemDataWrap.physicalDelete(wrapper);
    }


    /**
     * 提交单据
     *
     * @param ctx ctx
     */
    public void submit(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentRegisterHeadDTO po = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 设置上下操作日志类型
        ctx.setContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE, EnumReceiptOperationType.RECEIPT_OPERATION_SUBMIT);
        // 保存
        this.save(ctx);
        po.setSubmitTime(UtilDate.getNow()).setSubmitUserId(ctx.getCurrentUser().getId());
    }

    public void updateStatus(BizReceiptPaymentRegisterHeadDTO headDTO, List<BizReceiptPaymentRegisterItemDTO> itemDTOList,
                             Integer status) {
        if (UtilObject.isNull(headDTO)) {
            // 更新item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
        } else if (UtilCollection.isEmpty(itemDTOList)) {
            // 更新head状态
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        } else if (UtilCollection.isNotEmpty(itemDTOList)) {
            // 更新head、item状态
            itemDTOList.forEach(item -> item.setItemStatus(status));
            this.updateItem(itemDTOList);
            headDTO.setReceiptStatus(status);
            this.updateHead(headDTO);
        }
    }

    /**
     * 状态变更-草稿
     */
    public void updateStatusDraft(BizContext ctx) {
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_DRAFT.getValue());
    }

    /**
     * 状态变更-待处理
     */
    public void updateStatusWaitHandle(BizContext ctx) {
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_WAIT_HANDLE.getValue());
    }

    /**
     * 状态变更-已驳回
     */
    public void updateStatusRejected(BizContext ctx) {
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_REJECTED.getValue());
    }

    /**
     * 更新付款结算head状态
     *
     * @param headDto 付款结算head
     */
    private void updateHead(BizReceiptPaymentRegisterHeadDTO headDto) {
        if (UtilObject.isNotNull(headDto)) {
            bizReceiptPaymentRegisterHeadDataWrap.updateDtoById(headDto);
        }
    }

    /**
     * 更新付款结算item状态
     *
     * @param itemDtoList 付款结算item
     */
    private void updateItem(List<BizReceiptPaymentRegisterItemDTO> itemDtoList) {
        if (UtilCollection.isNotEmpty(itemDtoList)) {
            bizReceiptPaymentRegisterItemDataWrap.updateBatchDtoById(itemDtoList);
        }
    }


    /**
     * 保存附件
     *
     * @in ctx 入参 {@link BizReceiptInconformityHeadDTO : "要保持附件的不符合项单"}
     */
    public void saveBizReceiptAttachment(BizContext ctx) {
        // 入参上下文 - 要保持附件的不符合项单
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存不符合项单附件
        receiptAttachmentService.saveBizReceiptAttachment(headDTO.getFileList(), headDTO.getId(), headDTO.getReceiptType(), user.getId());
    }

    /**
     * 保存操作日志
     *
     * @in ctx 入参 {@link BizReceiptRegisterHeadDTO : "要保存操作日志的不符合项单"}
     */
    public void saveBizReceiptOperationLog(BizContext ctx) {
        // 入参上下文 -
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 入参上下文 - 操作类型
        EnumReceiptOperationType operationLogType = ctx.getContextData(Const.BIZ_CONTEXT_OPERATION_LOG_TYPE);
        // 当前登录用户
        CurrentUser user = ctx.getCurrentUser();
        // 保存操作日志
        receiptOperationLogService.saveBizReceiptOperationLogList(headDTO.getId(), headDTO.getReceiptType(), operationLogType, "", user.getId());
    }

    /**
     * 更新合同已支付金额
     *
     * @param ctx 上下文
     */
    public void updateContractPaidAmount(BizContext ctx) {
        // 入参上下文
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);

        // 更新合同已支付金额 = 抬头合同已支付金额 + 所有行项目计划付款金额
        // 老毕说不用管并发问题, 用户会手动维护好抬头合同已支付金额
        if (UtilNumber.isNotEmpty(headDTO.getContractId())) {
            BigDecimal reducePaymentAmount = headDTO.getItemList().stream().map(BizReceiptPaymentRegisterItemDTO::getPaymentAmount).filter(UtilNumber::isNotEmpty).reduce(BigDecimal.ZERO, BigDecimal::add);
            bizReceiptContractHeadDataWrap.update(new UpdateWrapper<BizReceiptContractHead>().lambda()
                    .set(BizReceiptContractHead::getPaidAmount, reducePaymentAmount.add(headDTO.getPaidAmount()))
                    .eq(BizReceiptContractHead::getId, headDTO.getContractId()));
        }
    }

    public void pullSrm(BizContext ctx) {
        // 入参上下文 -
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        dataFillService.fillAttr(headDTO);

        // 更新状态未同步
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_UNSYNC.getValue());

        // 油品结算”类型时，付款登记提交完不回传SRM付款信息
        // if (headDTO.getSettlementType() == 3) {
        //     // 更新状态已完成
        //     this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        //     // 更新付款计划已支付数量
        //     // this.updatePaidAmount(headDTO);
        //     // 判断付款结算是否支付完全
        //     // this.checkIsCompletePaid(headDTO);
        //     return;
        // }
        JSONArray headJson = this.getParam(headDTO);
        if (!headJson.isEmpty()) {
            Map<String, String> response = srmService.call(headJson.toJSONString(), createPaymentPlanUrl);
            if (response.containsKey("success")) {
                JSONObject jsonObject = JSONObject.parseObject(response.get("success"));
                if ("success".equals(jsonObject.getString("status"))) {
                    // 更新状态已完成
                    this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
                    // 更新付款计划已支付数量
                    // this.updatePaidAmount(headDTO);
                    // 判断付款结算是否支付完全
                    // this.checkIsCompletePaid(headDTO);
                } else {
                    log.warn("回传srm付款消息失败");
                }

            }
        } else {
            this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        }
    }

    public String getLangCodeFromRequest() {
        ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (ra == null) {
            return Const.DEFAULT_LANG_CODE;
        }
        HttpServletRequest request = ra.getRequest();
        String langCode = request.getHeader(Const.LANG_CODE_HEADER_NAME);
        return langCode == null ? Const.DEFAULT_LANG_CODE : langCode;
    }

    // private void checkIsCompletePaid(BizReceiptPaymentRegisterHeadDTO headDTO) {
    //     List<Long> paymentPlanIds = headDTO.getItemList().stream().map(BizReceiptPaymentRegisterItemDTO::getPaymentPlanId).collect(Collectors.toList());
    //     if (bizReceiptPaymentPlanHeadDataWrap.listByIds(paymentPlanIds).stream().allMatch(c -> c.getNotPaidAmount().equals(BigDecimal.ZERO))) {
    //         BizReceiptPaymentSettlementItem settlementItem = bizReceiptPaymentSettlementItemDataWrap.getById(headDTO.getItemList().get(0).getSettlementItemId());
    //         BizReceiptPaymentSettlementHead settlementHead = new BizReceiptPaymentSettlementHead();
    //         settlementHead.setId(settlementItem.getHeadId());
    //         settlementHead.setIsCompletePaid(EnumRealYn.TRUE.getIntValue());
    //         bizReceiptPaymentSettlementHeadDataWrap.updateById(settlementHead);
    //     }
    // }

    private void updatePaidAmount(BizReceiptPaymentRegisterHeadDTO headDTO) {
        // 资金计划类的
        if (headDTO.getSettlementType().equals(1)) {
            for (BizReceiptPaymentRegisterItemDTO registerItemDTO : headDTO.getItemList()) {
                bizReceiptPaymentPlanHeadDataWrap.update(new UpdateWrapper<BizReceiptPaymentPlanHead>().lambda().
                        setSql("paid_amount = paid_amount +" + registerItemDTO.getQty()).
                        setSql("not_paid_amount = not_paid_amount -" + registerItemDTO.getQty()).
                        eq(BizReceiptPaymentPlanHead::getId, registerItemDTO.getPaymentPlanId()));
            }
        } else {
            bizReceiptPaymentRegisterHeadDataWrap.update(new UpdateWrapper<BizReceiptPaymentRegisterHead>().lambda().
                    setSql("paid_amount = paid_amount +" + headDTO.getQty()).
                    setSql("not_paid_amount = not_paid_amount -" + headDTO.getQty()).
                    eq(BizReceiptPaymentRegisterHead::getId, headDTO.getId()));
        }

    }

    public JSONArray getParam(BizReceiptPaymentRegisterHeadDTO headDTO) {
        JSONObject jsonObject = new JSONObject();
        // 构造 JSONArray
        JSONArray jsonArray = new JSONArray();
        for (BizReceiptPaymentRegisterItemDTO registerItemDTO : headDTO.getItemList()) {
            // 备注
            jsonObject.put("remark", headDTO.getRegisterDesc());
            // 目前只有基于资金计划类的付款结算有付款节点，才回传srm // 2025-07-01 改成能殷支付的也要传srm 就油品结算的不传
            if (registerItemDTO.getSettlementType() != 3) {
                // 合同编号
                jsonObject.put("protocolNo", headDTO.getContractCode());
                // 付款节点
                jsonObject.put("payNode", "");
                // 付款金额
                jsonObject.put("payAmount", registerItemDTO.getPaymentAmount());
                // 币种
                jsonObject.put("priceCurrency", i18nTextCommonService.getNameMessage(this.getLangCodeFromRequest(), "contractCurrency", registerItemDTO.getContractCurrency().toString()));
                // 付款时间
                jsonObject.put("payTime", UtilDate.getStringDateTimeForDate(new Date()));
                // 本币币种
                jsonObject.put("priceCurrencyLocal", i18nTextCommonService.getNameMessage(this.getLangCodeFromRequest(), "invoiceCurrency", registerItemDTO.getInvoiceCurrency().toString()));
                // 本币金额
                jsonObject.put("payAmountLocal", registerItemDTO.getInvoiceAmount());
                jsonArray.add(jsonObject);
            }


        }

        return jsonArray;
    }


    public void selectPaymentSettlement(BizContext ctx) {
        // 上下文入参
        BizReceiptPaymentSettlementSearchPO po = ctx.getPoContextData();
        if (Objects.isNull(po)) {
            po = new BizReceiptPaymentSettlementSearchPO();
        }
        List<BizReceiptPaymentSettlementVO> resultList = bizReceiptPaymentRegisterHeadDataWrap.selectPaymentSettlement(po);
        // 设置返回信息到上下文
        ctx.setVoContextData(resultList);
    }

    public void getRegisterInfo(BizContext ctx) {
        // 入参上下文
        List<Long> headIds = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 获取详情
        List<BizReceiptPaymentSettlementHead> heads = bizReceiptPaymentSettlementHeadDataWrap.listByIds(headIds);
        // 转DTO
        List<BizReceiptPaymentSettlementHeadDTO> headDTOS =
                UtilCollection.toList(heads, BizReceiptPaymentSettlementHeadDTO.class);
        // 填充关联属性和父子属性
        dataFillService.fillAttr(headDTOS);
        BizReceiptPaymentRegisterHeadDTO paymentRegisterHeadDTO = new BizReceiptPaymentRegisterHeadDTO();
        UtilBean.copy(headDTOS.get(0), paymentRegisterHeadDTO);
        paymentRegisterHeadDTO.setId(null);
        paymentRegisterHeadDTO.setFileList(new ArrayList<>());
        paymentRegisterHeadDTO.setLogList(new ArrayList<>());
        paymentRegisterHeadDTO.setSettlementId(headDTOS.get(0).getId());
        List<BizReceiptPaymentInvoiceDTO> invoiceList =  new ArrayList<>();
        invoiceList = headDTOS.stream().map(BizReceiptPaymentSettlementHeadDTO::getInvoiceList).filter(UtilCollection::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
        paymentRegisterHeadDTO.setInvoiceList(invoiceList);
        List<BizReceiptPaymentRegisterItemDTO> list = new ArrayList<>();
        headDTOS.forEach(c -> {
            BizReceiptPaymentRegisterItemDTO registerItemDTO = new BizReceiptPaymentRegisterItemDTO();
            registerItemDTO.setSettlementHeadId(c.getId());
            dataFillService.fillAttr(registerItemDTO);
            registerItemDTO.setQty(c.getInvoiceAmount());
            list.add(registerItemDTO);
        });

        // 查询合同 已支付金额
        if (UtilNumber.isNotEmpty(paymentRegisterHeadDTO.getContractId())) {
            BizReceiptContractHead contractHead = bizReceiptContractHeadDataWrap.getById(paymentRegisterHeadDTO.getContractId());
            if (UtilObject.isNotNull(contractHead)) {
                paymentRegisterHeadDTO.setPaidAmount(contractHead.getPaidAmount());
            }
        }

        // 查询供应商 收款单位开户行、收款单位账号
        if (UtilNumber.isNotEmpty(paymentRegisterHeadDTO.getSupplierId())) {
            DicSupplier dicSupplier = dicSupplierDataWrap.getById(paymentRegisterHeadDTO.getSupplierId());
            if (UtilObject.isNotNull(dicSupplier)) {
                paymentRegisterHeadDTO.setPayeeBankName(dicSupplier.getPayeeBankName());
                paymentRegisterHeadDTO.setPayeeAccountNumber(dicSupplier.getPayeeAccountNumber());
            }
        }

        // 查询分项信息
        // if (!headDTO.getPurchaseType().equals(EnumPurchaseType.PRODUCTION_MATERIAL.getCode()) && !headDTO.getPurchaseType().equals(EnumPurchaseType.ASSET.getCode())) {
        //     List<BizReceiptContractSubItemDTO> subItemDTOList = this.getSubItemDTOList(headDTO.getItemList());
        //     paymentRegisterHeadDTO.setSubItemList(subItemDTOList);
        // }

        paymentRegisterHeadDTO.setItemList(list);
        // 设置返回信息到上下文
        ctx.setVoContextData(paymentRegisterHeadDTO);
    }


    private List<BizReceiptContractSubItemDTO> getSubItemDTOList(List<BizReceiptPaymentSettlementItemDTO> po) {
        List<BizReceiptContractSubItemDTO> subItemDTOList = new ArrayList<>();
        for (BizReceiptPaymentSettlementItemDTO itemDTO : po) {
            List<BizReceiptContractSubItem> subItems = bizReceiptContractSubItemDataWrap.list(new QueryWrapper<BizReceiptContractSubItem>().lambda().eq
                    (BizReceiptContractSubItem::getPaymentPlanId, itemDTO.getPaymentPlanHeadDTO().getId()));
            Map<String, List<BizReceiptContractSubItem>> map = subItems.stream().collect(Collectors.groupingBy(BizReceiptContractSubItem::getSubItemName));

            map.forEach((k, v) -> {
                BizReceiptContractSubItemDTO subItemDTO = new BizReceiptContractSubItemDTO();
                UtilBean.copy(v.get(0), subItemDTO);
                // 数量取合并后的
                subItemDTO.setPaymentPlanQty(v.stream().map(BizReceiptContractSubItem::getPaymentPlanQty).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTO.setPaymentPlanValue(v.stream().map(BizReceiptContractSubItem::getPaymentPlanValue).reduce(BigDecimal.ZERO, BigDecimal::add));
                subItemDTOList.add(subItemDTO);
            });

        }
        return subItemDTOList;
    }

    /**
     * 发起审批
     *
     * @param ctx 入参上下文
     */
    public void startWorkFlow(BizContext ctx) {
        BizReceiptPaymentRegisterHeadDTO headDTO = ctx.getContextData(Const.BIZ_CONTEXT_KEY_PO);
        // 发起流程审批
        Long receiptId = headDTO.getId();
        String receiptCode = headDTO.getReceiptCode();
        String receiptTypeStr = String.valueOf(headDTO.getReceiptType());
        if (EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode().equals(headDTO.getFirstParty())) {
            receiptTypeStr = receiptTypeStr + "1";
        } else if (EnumContractFirstParty.SHANGHAI_ENERGY.getCode().equals(headDTO.getFirstParty())) {
            receiptTypeStr = receiptTypeStr + "2";
        } else {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_PARAMETER_ERROR);
        }
        Map<String, Object> variables = new HashMap<>();

        // 审批人校验
        this.approveCheck(headDTO, variables);

        // 抄送人：华信JSCCHX 能殷JSCCNY
        List<String> ccUserCodeList = new ArrayList<>();
        if (EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode().equals(headDTO.getFirstParty())) {
            ccUserCodeList = sysUserDataWrap.getUserByRoleCode(Const.JSCCHX_ROLE_CODE).stream().map(SysUser::getUserCode).collect(Collectors.toList());
        } else if (EnumContractFirstParty.SHANGHAI_ENERGY.getCode().equals(headDTO.getFirstParty())) {
            ccUserCodeList = sysUserDataWrap.getUserByRoleCode(Const.JSCCNY_ROLE_CODE).stream().map(SysUser::getUserCode).collect(Collectors.toList());
        }
        variables.put("cc", ccUserCodeList);

        // 付款登记：“请审批”[公司+部门]用户姓名+“提交的流程”+付款登记描述（取付款登记抬头付款登记描述）
        variables.put("subject", "请审批[" + dictionaryService.getCorpCacheById(ctx.getCurrentUser().getCorpId()).getCorpName() + ctx.getCurrentUser().getUserDeptList().get(0).getDeptName() + "]" + ctx.getCurrentUser().getUserName() + "提交的流程：" + headDTO.getRegisterDesc());

        workflowService.setBizReceiptVariables(variables, receiptId, receiptCode, Integer.valueOf(receiptTypeStr), ctx, headDTO.getRegisterDesc());
        workflowService.startWorkFlow(receiptId, receiptCode, Integer.valueOf(receiptTypeStr), variables);

        // 更新单据状态审批中
        this.updateStatus(headDTO, headDTO.getItemList(), EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue());

        // 如果是驳回之后的再次提交，那驳回的时候给单据提交人发送了待办，因此在提交时，需要完成待办
        hXOaIntegerfaceService.completeTodo(HXOaIntegerfaceService.SEND_TYPE_DEFAULT, headDTO.getId().toString(), Arrays.asList(UtilCurrentContext.getCurrentUser().getUserCode()), headDTO.getReceiptCode());
    }

    /**
     * 审批人校验
     *
     * @param headDTO   待审批单据
     * @param variables 审批变量
     */
    private void approveCheck(BizReceiptPaymentRegisterHeadDTO headDTO, Map<String, Object> variables) {
        if (EnumContractFirstParty.SHANGHAI_ELECTRIC.getCode().equals(headDTO.getFirstParty())) {
            // 合同甲方为“华信”时: 开始-经营部部门领导-经营部分管领导-财务成本主管-财务费用主管-财务部部门领导-财务分管领导-华信（公司领导）李吉根-预制发票审核（孙铭）-结束
            // 经营部部门领导
            List<String> userList1 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_2);
            if (UtilCollection.isEmpty(userList1)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
            }
            // 经营部分管领导
            List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList2)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
            }
            // 财务成本主管
            List<String> userList3 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_6);
            if (UtilCollection.isEmpty(userList3)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            // 财务费用主管
            List<String> userList4 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_7);
            if (UtilCollection.isEmpty(userList4)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "4");
            }
            // 财务部部门领导
            List<String> userList5 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_2);
            if (UtilCollection.isEmpty(userList5)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "5");
            }
            // 财务分管领导
            List<String> userList6 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList6)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "6");
            }
            // 华信（公司领导）李吉根 10100212
            List<SysUser> userList7 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100212"));
            if (UtilCollection.isEmpty(userList7)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "7");
            }
            variables.put("level7UserCode", "10100212");
            // 预制发票审核（孙铭）12200245
            List<SysUser> userList8 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "12200245"));
            if (UtilCollection.isEmpty(userList8)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "8");
            }
            variables.put("level8UserCode", "12200245");
        } else if (EnumContractFirstParty.SHANGHAI_ENERGY.getCode().equals(headDTO.getFirstParty())) {
            // 合同甲方为“能殷”时: 开始-业务需求部门分管领导（马永海）-经营部部门领导-经营部分管领导-财务经理-财务分管领导-华信（公司领导）李吉根-能殷（公司领导）林申晟-财务出纳-结束
            // 业务需求部门分管领导（马永海）11800262
            List<SysUser> userList1 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "11800262"));
            if (UtilCollection.isEmpty(userList1)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "1");
            }
            variables.put("level1UserCode", "11800262");
            // 经营部部门领导
            List<String> userList2 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_2);
            if (UtilCollection.isEmpty(userList2)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "2");
            }
            // 经营部分管领导
            List<String> userList3 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.BMD.getCode(), null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList3)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "3");
            }
            // 财务经理
            List<String> userList4 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD2.getCode(), null, EnumApprovalLevel.LEVEL_8);
            if (UtilCollection.isEmpty(userList4)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "4");
            }
            // 财务分管领导
            List<String> userList5 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD.getCode(), null, EnumApprovalLevel.LEVEL_3);
            if (UtilCollection.isEmpty(userList5)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "5");
            }
            // 华信（公司领导）李吉根 10100212
            List<SysUser> userList6 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100212"));
            if (UtilCollection.isEmpty(userList6)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "6");
            }
            variables.put("level6UserCode", "10100212");
            // 能殷（公司领导）林申晟 10100714
            List<SysUser> userList7 = sysUserDataWrap.list(new QueryWrapper<SysUser>().lambda().eq(SysUser::getUserCode, "10100714"));
            if (UtilCollection.isEmpty(userList7)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "7");
            }
            variables.put("level7UserCode", "10100714");
            // 财务出纳
            List<String> userList8 = sysUserDeptOfficeRelDataWrap.getApproveUserCode(EnumDept.FD2.getCode(), null, EnumApprovalLevel.LEVEL_9);
            if (UtilCollection.isEmpty(userList8)) {
                throw new WmsException(EnumReturnMsg.RETURN_CODE_APPROVE_NO_USER_DEPT, "8");
            }
        }
    }

    /**
     * 审批回调
     *
     * @in ctx 入参 {@link BizApprovalReceiptInstanceRelDTO ："回调参数"}
     */
    public void approvalCallback(BizApprovalReceiptInstanceRelDTO wfReceiptCo) {
        BizContext ctx = new BizContext();
        CurrentUser currentUser = wfReceiptCo.getInitiator();
        ctx.setCurrentUser(currentUser);
        BizReceiptPaymentRegisterHead head = bizReceiptPaymentRegisterHeadDataWrap.getById(wfReceiptCo.getReceiptHeadId());
        BizReceiptPaymentRegisterHeadDTO headDTO = UtilBean.newInstance(head, BizReceiptPaymentRegisterHeadDTO.class);
        dataFillService.fillAttr(headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        // 审批通过
        if (wfReceiptCo.getApproveStatus().equals(EnumApprovalStatus.FINISH.getValue())) {

            // 更新单据状态待处理
            this.updateStatusWaitHandle(ctx);

            // 向抄送人发送待办
            HistoricVariableInstance cc = historyServiceImpl.createHistoricVariableInstanceQuery().processInstanceId(wfReceiptCo.getProcessInstanceId()).variableName("cc").singleResult();
            if (UtilObject.isNotNull(cc)) {
                List<String> userCodeList = (List<String>) cc.getValue();
                if (UtilCollection.isNotEmpty(userCodeList)) {
                    hXOaIntegerfaceService.sendTodo(HXOaIntegerfaceService.SEND_TYPE_NOTICE, "请查看" + head.getReceiptCode() + "付款登记的抄送", Const.STRING_EMPTY, head.getId().toString(), userCodeList, head.getReceiptCode());
                }
            }
        }else {

            // 更新单据状态已驳回
            this.updateStatusRejected(ctx);
        }
    }

    /**
     * 撤销流程
     *
     * @param ctx 上下文
     */
    public void revokeProcessInstance(BizContext ctx) {
        BizResultVO<BizReceiptPaymentRegisterHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        BizReceiptPaymentRegisterHeadDTO headDTO = vo.getHead();
        if (EnumReceiptStatus.RECEIPT_STATUS_APPROVING.getValue().equals(headDTO.getReceiptStatus())) {
            // 删除待办:必须在审批撤销前
            String id = workflowService.deleteTodo(headDTO.getId());
            // 审批撤销
            RevokeDTO revokeDTO = new RevokeDTO();
            revokeDTO.setProcessInstanceId(id);
            workflowService.revoke(revokeDTO);
        }
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_PO, headDTO);
        ctx.setContextData(Const.BIZ_CONTEXT_KEY_CODE, headDTO.getReceiptCode());
    }

}
