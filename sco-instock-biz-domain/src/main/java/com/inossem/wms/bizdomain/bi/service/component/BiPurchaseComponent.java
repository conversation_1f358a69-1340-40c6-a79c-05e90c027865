package com.inossem.wms.bizdomain.bi.service.component;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.inossem.wms.bizdomain.bi.dao.BiPurchaseMapper;
import com.inossem.wms.bizdomain.bi.service.datawrap.BiPurchaseBudgetYearDataWrap;
import com.inossem.wms.bizdomain.bi.service.datawrap.BiPurchaseContractDataWrap;
import com.inossem.wms.bizdomain.bi.service.datawrap.BiPurchaseDemandPlanDataWrap;
import com.inossem.wms.bizdomain.bi.service.datawrap.BiPurchasePurchaseDataWrap;
import com.inossem.wms.bizdomain.budget.service.datawrap.AnnualBudgetDataWrap;
import com.inossem.wms.bizdomain.contract.service.datawrap.BizReceiptContractHeadDataWrap;
import com.inossem.wms.bizdomain.exchangerate.service.biz.ExchangeRateService;
import com.inossem.wms.bizdomain.purchase.service.datawrap.BizReceiptPurchaseApplyHeadDataWrap;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.constant.mq.TagConst;
import com.inossem.wms.common.enums.EnumReceiptStatus;
import com.inossem.wms.common.enums.EnumReceiptType;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.enums.demandplan.EnumDemandPlanType;
import com.inossem.wms.common.exception.WmsException;
import com.inossem.wms.common.model.auth.user.vo.CurrentUser;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseBase;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseBudgetYear;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseContract;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchaseDemandPlan;
import com.inossem.wms.common.model.bizdomain.bi.entity.BiPurchasePurchase;
import com.inossem.wms.common.model.bizdomain.bi.po.BiBudgetSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.po.BiPurchaseBudgetYearImportPO;
import com.inossem.wms.common.model.bizdomain.bi.po.BiSearchPO;
import com.inossem.wms.common.model.bizdomain.bi.vo.*;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractHead;
import com.inossem.wms.common.model.bizdomain.purchase.entity.BizReceiptPurchaseApplyHead;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.file.file.entity.BizCommonFile;
import com.inossem.wms.common.model.masterdata.budget.entity.DicAnnualBudget;
import com.inossem.wms.common.rocketmq.ProducerMessageContent;
import com.inossem.wms.common.rocketmq.RocketMQProducerProcessor;
import com.inossem.wms.common.util.UtilCollection;
import com.inossem.wms.common.util.UtilDate;
import com.inossem.wms.common.util.UtilNumber;
import com.inossem.wms.common.util.UtilObject;
import com.inossem.wms.common.util.excel.UtilExcel;
import com.inossem.wms.system.file.service.biz.FileService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * BI-华信资源驾驶舱-采购类指标 代码组件
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
@Slf4j
public class BiPurchaseComponent {

    @Autowired
    private BiPurchaseMapper biPurchaseMapper;
    @Autowired
    private FileService fileService;
    @Autowired
    private ExchangeRateService exchangeRateService;
    @Autowired
    private BizReceiptContractHeadDataWrap contractHeadDataWrap;
    @Autowired
    private BizReceiptPurchaseApplyHeadDataWrap purchaseApplyHeadDataWrap;
    @Autowired
    private BiPurchaseDemandPlanDataWrap biPurchaseDemandPlanDataWrap;
    @Autowired
    private BiPurchasePurchaseDataWrap biPurchasePurchaseDataWrap;
    @Autowired
    private BiPurchaseContractDataWrap biPurchaseContractDataWrap;
    @Autowired
    private BiPurchaseBudgetYearDataWrap biPurchaseBudgetYearDataWrap;
    @Autowired
    private AnnualBudgetDataWrap annualBudgetDataWrap;

    // 1万
    private final BigDecimal TEN_THOUSAND = BigDecimal.valueOf(10000);

    // 1百
    private final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    /**
     * BI-已完成采购任务的合同数量/金额
     */
    public void getCompletedContractSum(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));

        BiCompletedContractSumVO vo = new BiCompletedContractSumVO();
        vo.setYear(po.getYear());

        // 查询已完成采购任务合同数量
        QueryWrapper<BizReceiptContractHead> contractCountQueryWrapper = new QueryWrapper<>();
        contractCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        contractCountQueryWrapper.lambda().in(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue(), EnumReceiptType.FRAMEWORK_CONTRACT.getValue());
        contractCountQueryWrapper.lambda().eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        vo.setTotalCount(contractHeadDataWrap.count(contractCountQueryWrapper));

        // 查询已完成采购任务合同金额详情（包含币种和创建时间）
        List<BiContractAmountDetailVO> contractDetails = biPurchaseMapper.selectContractDetailForAmountSum(po.getYear());

        // 进行汇率转换，将所有金额转换为美元
        BigDecimal totalUsdAmount = BigDecimal.ZERO;
        for (BiContractAmountDetailVO detail : contractDetails) {
            if (UtilNumber.isNotEmpty(detail.getAmount())) {
                BigDecimal usdAmount = exchangeRateService.convertToUSD(detail.getCurrency(), detail.getAmount(), detail.getYear(), detail.getMonth());
                totalUsdAmount = totalUsdAmount.add(usdAmount);
            }
        }

        if (UtilNumber.isNotEmpty(totalUsdAmount)) {
            vo.setTotalAmount(totalUsdAmount.divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
        }

        // 查询完成招投标次数
        QueryWrapper<BizReceiptPurchaseApplyHead> purchaseCountQueryWrapper = new QueryWrapper<>();
        purchaseCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        purchaseCountQueryWrapper.lambda().in(BizReceiptPurchaseApplyHead::getReceiptType, EnumReceiptType.PURCHASE_APPLY.getValue(), EnumReceiptType.OIL_PO_PURCHASE.getValue(), EnumReceiptType.CONTRACT_CHANGE.getValue(), EnumReceiptType.DIRECT_PURCHASE.getValue());
        purchaseCountQueryWrapper.lambda().eq(BizReceiptPurchaseApplyHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        vo.setPurchaseCount(purchaseApplyHeadDataWrap.count(purchaseCountQueryWrapper));

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * BI-已完成采购任务的合同数量
     */
    public void getCompletedContractCount(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));
        int month = UtilNumber.isNotEmpty(po.getMonth()) ? po.getMonth() : DateUtil.month(UtilDate.getNow()) + 1;

        // 查询当前各个月的已完成采购任务的合同数量
        List<BiCompletedContractCountVO> voList = biPurchaseMapper.getCompletedContractCount(po);

        Map<Integer, BiCompletedContractCountVO> voMonthMap = voList.stream().collect(Collectors.toMap(BiCompletedContractCountVO::getMonth, v -> v));

        // 补充缺失月份数据
        for (int i = 1; i <= month; i++) {
            if (!voMonthMap.containsKey(i)) {
                BiCompletedContractCountVO vo = new BiCompletedContractCountVO();
                vo.setMonth(i);
                voList.add(vo);
            }
        }

        voList.sort(Comparator.comparing(BiCompletedContractCountVO::getMonth));

        // 查询已完成采购任务合同数量
        QueryWrapper<BizReceiptContractHead> contractCountQueryWrapper = new QueryWrapper<>();
        contractCountQueryWrapper.eq("YEAR(create_time)", po.getYear());
        contractCountQueryWrapper.lambda().in(BizReceiptContractHead::getReceiptType, EnumReceiptType.OTHER_CONTRACT.getValue(), EnumReceiptType.FRAMEWORK_CONTRACT.getValue());
        contractCountQueryWrapper.lambda().eq(BizReceiptContractHead::getReceiptStatus, EnumReceiptStatus.RECEIPT_STATUS_COMPLETED.getValue());
        long countTotal = contractHeadDataWrap.count(contractCountQueryWrapper);

        for (BiCompletedContractCountVO vo : voList) {
            // 万元单位
            vo.setMonthStr(vo.getMonth() + "月");
            if (UtilNumber.isNotEmpty(countTotal)) {
                vo.setCountTotalProportion(vo.getCountTotal().divide(BigDecimal.valueOf(countTotal), 1, RoundingMode.HALF_UP).multiply(ONE_HUNDRED));
            }
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, voList);
    }

    /**
     * BI-已完成采购任务的合同金额
     */
    public void getCompletedContractAmount(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));
        int month = UtilNumber.isNotEmpty(po.getMonth()) ? po.getMonth() : DateUtil.month(UtilDate.getNow()) + 1;

        // 查询当前各个月的已完成采购任务的合同金额
        List<BiCompletedContractAmountVO> amount402List = biPurchaseMapper.getCompletedContractAmount402(po);
        // 查询当前各个月的已完成采购任务的合同金额
        List<BiCompletedContractAmountVO> amount403List = biPurchaseMapper.getCompletedContractAmount403(po);

        for (BiCompletedContractAmountVO amount402 : amount402List) {
            for (BiCompletedContractAmountVO amount403 : amount403List) {
                if (amount402.getMonth().equals(amount403.getMonth())) {
                    amount402.setAmount403(amount403.getAmount403());
                    break;
                }
            }
        }

        Map<Integer, BiCompletedContractAmountVO> voMonthMap = amount402List.stream().collect(Collectors.toMap(BiCompletedContractAmountVO::getMonth, v -> v));

        // 补充缺失月份数据
        for (int i = 1; i <= month; i++) {
            if (!voMonthMap.containsKey(i)) {
                BiCompletedContractAmountVO vo = new BiCompletedContractAmountVO();
                vo.setMonth(i);
                amount402List.add(vo);
            }
        }

        amount402List.sort(Comparator.comparing(BiCompletedContractAmountVO::getMonth));

        // 查询已完成采购任务合同金额
        BigDecimal amountTotal = BigDecimal.ZERO;
        BigDecimal contractAmountSum = biPurchaseMapper.selectContractAmountSum(po);
        if (UtilNumber.isNotEmpty(contractAmountSum)) {
            amountTotal = contractAmountSum.divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP);
        }

        for (BiCompletedContractAmountVO vo : amount402List) {
            // 万元单位
            vo.setMonthStr(vo.getMonth() + "月");
            vo.setAmount402(vo.getAmount402().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setAmount403(vo.getAmount403().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setAmountTotal(vo.getAmount402().add(vo.getAmount403()).divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            if (UtilNumber.isNotEmpty(amountTotal)) {
                vo.setAmountTotalProportion(vo.getAmountTotal().divide(amountTotal, 1, RoundingMode.HALF_UP).multiply(ONE_HUNDRED));
            }
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, amount402List);
    }

    /**
     * BI-定时任务-保存采购类指标基础数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBaseTable() {
        log.info("开始执行BI采购类指标基础数据同步任务");

        try {
            // 1. 保存需求计划数据
            this.saveDemandPlanData();

            // 2. 保存采购申请数据
            this.savePurchaseData();

            // 3. 保存合同数据
            this.saveContractData();

            log.info("BI采购类指标基础数据同步任务执行完成");
        } catch (Exception e) {
            log.error("BI采购类指标基础数据同步任务执行失败", e);
            throw e;
        }
    }

    /**
     * 保存需求计划数据到 bi_purchase_demand_plan 表
     */
    private void saveDemandPlanData() {
        log.info("开始同步需求计划数据");

        // 查询需求计划单
        List<BiPurchaseDemandPlanVO> demandPlanList = biPurchaseMapper.selectDemandPlanHead();

        // 转换为实体对象
        List<BiPurchaseDemandPlan> entityList = UtilCollection.toList(demandPlanList, BiPurchaseDemandPlan.class);

        // 删除原有数据并保存新数据
        biPurchaseDemandPlanDataWrap.remove(new QueryWrapper<>());
        if (UtilCollection.isNotEmpty(entityList)) {
            biPurchaseDemandPlanDataWrap.saveBatch(entityList);
            log.info("成功同步需求计划数据 {} 条", entityList.size());
        }
    }

    /**
     * 保存采购申请数据到 bi_purchase_purchase 表
     */
    private void savePurchaseData() {
        log.info("开始同步采购申请数据");

        // 查询采购申请单
        List<BiPurchasePurchaseVO> purchaseList = biPurchaseMapper.selectPurchaseHead();

        // 转换为实体对象
        List<BiPurchasePurchase> entityList = UtilCollection.toList(purchaseList, BiPurchasePurchase.class);

        // 删除原有数据并保存新数据
        biPurchasePurchaseDataWrap.remove(new QueryWrapper<>());
        if (UtilCollection.isNotEmpty(entityList)) {
            biPurchasePurchaseDataWrap.saveBatch(entityList);
            log.info("成功同步采购申请数据 {} 条", entityList.size());
        }
    }

    /**
     * 保存合同数据到 bi_purchase_contract 表
     */
    private void saveContractData() {
        log.info("开始同步合同数据");

        // 查询合同单
        List<BiPurchaseContractVO> contractList = biPurchaseMapper.selectContractHead();

        // 转换为实体对象
        List<BiPurchaseContract> entityList = UtilCollection.toList(contractList, BiPurchaseContract.class);

        // 删除原有数据并保存新数据
        biPurchaseContractDataWrap.remove(new QueryWrapper<>());
        if (UtilCollection.isNotEmpty(entityList)) {
            biPurchaseContractDataWrap.saveBatch(entityList);
            log.info("成功同步合同数据 {} 条", entityList.size());
        }
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseByDemandPlan(BiPurchaseDemandPlanVO demandPlanVO, BiPurchaseBase biPurchaseBase) {
        // 需求计划单ID
        biPurchaseBase.setDemandReceiptHeadId(demandPlanVO.getId());
        // 需求计划单号
        biPurchaseBase.setDemandReceiptCode(demandPlanVO.getReceiptCode());
        // 需求计划创建时间
        biPurchaseBase.setDemandCreateTime(demandPlanVO.getCreateTime());
        // 需求计划创建人
        biPurchaseBase.setDemandCreateUserId(demandPlanVO.getCreateUserId());
        // 需求计划单处理人
        biPurchaseBase.setDemandHandleUserId(demandPlanVO.getHandleUserId());
        // 需求计划完成时间
        biPurchaseBase.setDemandModifyTime(demandPlanVO.getModifyTime());
        // 需求计划计划类型
        biPurchaseBase.setDemandPlanType(demandPlanVO.getDemandPlanType());
        // 需求计划需求类型
        biPurchaseBase.setDemandType(demandPlanVO.getDemandType());
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseBaseByPurchase(BiPurchasePurchaseVO purchaseVO, BiPurchaseBase biPurchaseBase) {
        // 采购申请单ID
        biPurchaseBase.setPurchaseReceiptHeadId(purchaseVO.getId());
        // 采购申请单号
        biPurchaseBase.setPurchaseReceiptCode(purchaseVO.getReceiptCode());
        // 采购申请单据类型
        biPurchaseBase.setPurchaseReceiptType(purchaseVO.getReceiptType());
        // 采购申请类型
        biPurchaseBase.setPurchaseDemandPlanType(purchaseVO.getDemandPlanType());
        // 采购申请采购方式
        biPurchaseBase.setPurchaseBidMethod(purchaseVO.getBidMethod());
        // 采购申请采购主体
        biPurchaseBase.setPurchaseSubject(purchaseVO.getPurchaseSubject());
        // 采购申请预算出处
        biPurchaseBase.setPurchaseAnnualBudgetId(purchaseVO.getAnnualBudgetId());
        // 采购申请申请预算金额
        biPurchaseBase.setPurchaseBudgetAmount(purchaseVO.getBudgetAmount());
        // 采购申请创建时间
        biPurchaseBase.setPurchaseCreateTime(purchaseVO.getCreateTime());
        // 采购申请创建人
        biPurchaseBase.setPurchaseCreateUserId(purchaseVO.getCreateUserId());
        // 需求计划完成时间
        biPurchaseBase.setDemandModifyTime(purchaseVO.getModifyTime());
        // 寻源时间（天） = 采购申请创建时间 - 需求计划完成时间
        if (UtilObject.isNotNull(biPurchaseBase.getPurchaseCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
            biPurchaseBase.setPurchaseSourcingTime(DateUtil.betweenDay(biPurchaseBase.getPurchaseCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
        }
    }

    /**
     * 设置BI采购基础表传输对象字段
     */
    private void setBiPurchaseByContract(BiPurchaseContractVO contractVO, BiPurchaseBase biPurchaseBase) {
        // 合同单ID
        biPurchaseBase.setContractReceiptHeadId(contractVO.getId());
        // 合同单号
        biPurchaseBase.setContractReceiptCode(contractVO.getReceiptCode());
        // 合同单据类型
        biPurchaseBase.setContractReceiptType(contractVO.getReceiptType());
        // 合同类型
        biPurchaseBase.setContractFirstParty(contractVO.getFirstParty());
        // 合同采购方式
        biPurchaseBase.setContractSupplierId(contractVO.getSupplierId());
        // 合同采购主体
        biPurchaseBase.setContractOilPrice(contractVO.getOilPrice());
        // 合同预算出处
        biPurchaseBase.setContractAmount(contractVO.getAmount());
        // 合同创建时间
        biPurchaseBase.setContractCreateTime(contractVO.getCreateTime());
        // 定价审批时间（天） = 合同创建时间 - 采购申请创建时间
        // 定价审批时间（天）框架协议PO = 合同创建时间 - 需求计划完成时间
        if (UtilNumber.isNotEmpty(biPurchaseBase.getDemandPlanType()) && EnumDemandPlanType.FRAMEWORK_AGREEMENT_PO_PlAN.getCode().equals(biPurchaseBase.getDemandPlanType())) {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
                biPurchaseBase.setPriceApprovalInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
            }
        } else {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getPurchaseCreateTime())) {
                biPurchaseBase.setPriceApprovalInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getPurchaseCreateTime(), true));
            }
        }
        // 采购阶段时间（天） = 合同创建时间 - 采购申请完成时间
        // 采购阶段时间（天）框架协议PO = 合同创建时间 - 需求计划完成时间
        if (UtilNumber.isNotEmpty(biPurchaseBase.getDemandPlanType()) && EnumDemandPlanType.FRAMEWORK_AGREEMENT_PO_PlAN.getCode().equals(biPurchaseBase.getDemandPlanType())) {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandModifyTime())) {
                biPurchaseBase.setPurchaseStageInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandModifyTime(), true));
            }
        } else {
            if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getPurchaseModifyTime())) {
                biPurchaseBase.setPurchaseStageInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getPurchaseModifyTime(), true));
            }
        }
        // 合同签订时间（天） = 合同创建时间 - 需求计划创建时间
        if (UtilObject.isNotNull(biPurchaseBase.getContractCreateTime()) && UtilObject.isNotNull(biPurchaseBase.getDemandCreateTime())) {
            biPurchaseBase.setContractSignInterval(DateUtil.betweenDay(biPurchaseBase.getContractCreateTime(), biPurchaseBase.getDemandCreateTime(), true));
        }
    }

    /**
     * BI采购年度预算金额表-导入数据
     */
    @SneakyThrows
    public void importBudgetYearData(BizContext ctx) {
        // 获取Excel附件
        MultipartFile file = ctx.getFileContextData();

        // 获取EXCEL数据
        List<BiPurchaseBudgetYearImportPO> importDataList = (List<BiPurchaseBudgetYearImportPO>) UtilExcel.readExcelData(file.getInputStream(), BiPurchaseBudgetYearImportPO.class);

        if (UtilCollection.isEmpty(importDataList)) {
            throw new WmsException(EnumReturnMsg.RETURN_CODE_IMOPRT_TEMPLATE_HAS_NO_DATA);
        }

        // 数据验证
        this.checkBudgetYearImportData(importDataList);

        // 转换为实体对象
        List<BiPurchaseBudgetYear> entityList = UtilCollection.toList(importDataList, BiPurchaseBudgetYear.class);

        // 按年份更新或新增数据
        if (UtilCollection.isNotEmpty(entityList)) {
            // 先查询所有已存在的年度数据
            List<BiPurchaseBudgetYear> existDataList = biPurchaseBudgetYearDataWrap.list();
            Map<Integer, BiPurchaseBudgetYear> yearBudgetMap = existDataList.stream().collect(Collectors.toMap(BiPurchaseBudgetYear::getBudgetYear, Function.identity()));

            // 分类待更新和待新增的数据
            List<BiPurchaseBudgetYear> updateList = new ArrayList<>();
            List<BiPurchaseBudgetYear> insertList = new ArrayList<>();

            for (BiPurchaseBudgetYear entity : entityList) {
                BiPurchaseBudgetYear existData = yearBudgetMap.get(entity.getBudgetYear());
                if (UtilObject.isNotNull(existData)) {
                    entity.setId(existData.getId());
                    updateList.add(entity);
                } else {
                    insertList.add(entity);
                }
            }

            // 批量更新和新增
            if (UtilCollection.isNotEmpty(updateList)) {
                biPurchaseBudgetYearDataWrap.updateBatchById(updateList);
            }
            if (UtilCollection.isNotEmpty(insertList)) {
                biPurchaseBudgetYearDataWrap.saveBatch(insertList);
            }
        }
    }

    /**
     * 验证年度预算金额导入数据
     */
    private void checkBudgetYearImportData(List<BiPurchaseBudgetYearImportPO> importDataList) {
        Set<Integer> yearSet = new HashSet<>();
        for (int i = 0; i < importDataList.size(); i++) {
            BiPurchaseBudgetYearImportPO item = importDataList.get(i);
            // Excel行号（第1行是标题）
            int rowNum = i + 2;

            // 验证年份
            if (item.getBudgetYear() == null) {
                throw new RuntimeException(String.format("第%d行：年份不能为空", rowNum));
            }
            if (item.getBudgetYear() < 1900 || item.getBudgetYear() > 2100) {
                throw new RuntimeException(String.format("第%d行：年份必须在1900-2100之间", rowNum));
            }
            // 检查年份重复
            if (yearSet.contains(item.getBudgetYear())) {
                throw new RuntimeException(String.format("第%d行：年份%d重复", i + 2, item.getBudgetYear()));
            }
            yearSet.add(item.getBudgetYear());

            // 验证预算金额
            if (UtilNumber.isEmpty(item.getBudgetAmount())) {
                throw new RuntimeException(String.format("第%d行：预算金额不能为空", rowNum));
            }
            if (item.getBudgetAmount().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException(String.format("第%d行：预算金额不能为负数", rowNum));
            }
        }
    }

    /**
     * BI采购年度预算金额表-导出数据
     */
    public void exportBudgetYearExcel(BizContext ctx) {
        CurrentUser user = ctx.getCurrentUser();
        BizCommonFile bizCommonFile = new BizCommonFile();
        bizCommonFile.setFileCode(FileService.genFileCode(Const.XLSX));
        bizCommonFile.setFileName(FileService.genFileName("年度预算金额"));
        bizCommonFile.setFileExt(Const.XLSX);
        bizCommonFile.setCreateUserId(user.getId());
        bizCommonFile.setModifyUserId(user.getId());

        // 生成前序数据时不要异步处理，否则后续流程可能无法按预期查询到前面异步插入的数据
        fileService.saveFileByUser(bizCommonFile);

        // 查询所有数据
        List<BiPurchaseBudgetYear> entityList = biPurchaseBudgetYearDataWrap.list();

        // 转换为导出VO
        List<BiPurchaseBudgetYearExportVO> exportList = UtilCollection.toList(entityList, BiPurchaseBudgetYearExportVO.class);

        // 写入Excel
        UtilExcel.writeExcel(BiPurchaseBudgetYearExportVO.class, exportList, bizCommonFile);

        // 推送MQ更新信息
        ProducerMessageContent updateMessage = ProducerMessageContent.messageContent(TagConst.UPDATE_USER_FILE, bizCommonFile);
        RocketMQProducerProcessor.getInstance().AsyncMQSend(updateMessage);
    }

    /**
     * BI-采购类指标-预算达成率-总和
     */
    public void getBudgetSum(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));

        BiBudgetSumVO vo = new BiBudgetSumVO();
        vo.setYear(po.getYear());

        // 查询年度预算金额
        LambdaQueryWrapper<BiPurchaseBudgetYear> yearWrapper = new LambdaQueryWrapper<>();
        yearWrapper.eq(BiPurchaseBudgetYear::getBudgetYear, po.getYear());
        BiPurchaseBudgetYear budgetYear = biPurchaseBudgetYearDataWrap.getOne(yearWrapper, false);
        if (UtilObject.isNotNull(budgetYear)) {
            vo.setBudgetAmount(budgetYear.getBudgetAmount().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
        }

        // 查询已有合同金额总和
        LambdaQueryWrapper<DicAnnualBudget> budgetWrapper = new LambdaQueryWrapper<>();
        budgetWrapper.eq(DicAnnualBudget::getYear, po.getYear());
        List<DicAnnualBudget> annualBudgetList = annualBudgetDataWrap.list(budgetWrapper);
        BigDecimal contractAmount = annualBudgetList.stream().map(DicAnnualBudget::getExistingContractAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        vo.setContractAmount(contractAmount.divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));

        // 计算预算达成率
        if (UtilNumber.isNotEmpty(vo.getBudgetAmount())) {
            BigDecimal amountRate = vo.getContractAmount().divide(vo.getBudgetAmount(), 3, RoundingMode.HALF_UP);
            vo.setAmountRate(amountRate.multiply(ONE_HUNDRED));
        }

        ctx.setContextData(Const.BIZ_CONTEXT_KEY_VO, vo);
    }

    /**
     * BI-采购类指标-预算分类
     */
    public void getBudgetClassify(BizContext ctx) {
        BiSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));

        // 查询预算分类列表
        List<BiBudgetClassifySubjectVO> voList = biPurchaseMapper.selectBudgetClassifyList(po);

        for (BiBudgetClassifySubjectVO vo : voList) {
            vo.setBudgetAmount(vo.getBudgetAmount().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setContractAmount(vo.getContractAmount().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            if (UtilNumber.isNotEmpty(vo.getBudgetAmount())) {
                BigDecimal amountRate = vo.getContractAmount().divide(vo.getBudgetAmount(), 3, RoundingMode.HALF_UP);
                vo.setAmountRate(amountRate.multiply(ONE_HUNDRED));
            }
        }

        // 设置上下文
        ctx.setVoContextData(voList);
    }

    /**
     * BI-采购类指标-预算科目
     */
    public void getBudgetSubject(BizContext ctx) {
        BiBudgetSearchPO po = ctx.getPoContextData();
        po.setYear(UtilNumber.isNotEmpty(po.getYear()) ? po.getYear() : DateUtil.year(UtilDate.getNow()));

        // 查询预算科目列表
        List<BiBudgetClassifySubjectVO> voList = biPurchaseMapper.selectBudgetSubjectList(po);

        for (BiBudgetClassifySubjectVO vo : voList) {
            vo.setBudgetAmount(vo.getBudgetAmount().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            vo.setContractAmount(vo.getContractAmount().divide(TEN_THOUSAND, 1, RoundingMode.HALF_UP));
            if (UtilNumber.isNotEmpty(vo.getBudgetAmount())) {
                BigDecimal amountRate = vo.getContractAmount().divide(vo.getBudgetAmount(), 3, RoundingMode.HALF_UP);
                vo.setAmountRate(amountRate.multiply(ONE_HUNDRED));
            }
        }

        // 设置上下文
        ctx.setVoContextData(voList);
    }

}
