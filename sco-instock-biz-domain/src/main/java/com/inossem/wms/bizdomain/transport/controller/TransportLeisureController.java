package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportLeisureService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.register.po.BizLabelPrintPO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.common.base.*;
import com.inossem.wms.common.model.print.label.LabelTransportLeisureBox;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 闲置物资转库
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "闲置管理")
@Deprecated
public class TransportLeisureController {

    @Autowired
    private TransportLeisureService transportLeisureService;

    /**
     * 页面初始化
     *
     * @in po 查询条件
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"闲置管理-闲置物资转库"})
    @PostMapping(value = "/transport/leisure/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportLeisureService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"闲置管理-闲置物资转库"})
    @PostMapping(value = "/transport/leisure/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportLeisureService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 没有分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"闲置管理-闲置物资转库"})
    @PostMapping(value = "/transport/leisure/list")
    public BaseResult getList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportLeisureService.getList(ctx);
        MultiResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }


    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"闲置管理-闲置物资转库"})
    @GetMapping(value = "/transport/leisure/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportLeisureService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"闲置管理-闲置物资转库"})
    @PostMapping(value = "/transport/leisure/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportLeisureService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"闲置管理-闲置物资转库"})
    @PostMapping(value = "/transport/leisure/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportLeisureService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"闲置管理-闲置物资转库"})
    @DeleteMapping("/transport/leisure/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportLeisureService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 闲置物资转库-打印
     * @auth zhibo
     * @param po 打印入参
     * @param ctx 请求上下文
     * @return 闲置物资转库
     */
    @ApiOperation(value = "验收入库单-物料标签打印-PDA", tags = {"入库管理-验收入库"})
    @PostMapping(value = "/transport/leisure/box-label-print")
    public BaseResult<?> boxLabelPrint(@RequestBody BizLabelPrintPO<BizReceiptTransportHeadDTO> po, BizContext ctx) {
        transportLeisureService.boxApplyLabelPrint(ctx);
        List<LabelTransportLeisureBox> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(new MultiResultVO<>(vo));
    }

}