package com.inossem.wms.bizdomain.transport.controller;

import com.inossem.wms.bizdomain.transport.service.biz.TransportService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.enums.EnumReturnMsg;
import com.inossem.wms.common.model.auth.user.dto.SysUserDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.arrange.po.BizReceiptArrangeSearchPo;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportItemDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import com.inossem.wms.common.model.erp.dto.ErpWbs;
import com.inossem.wms.common.model.masterdata.base.entity.DicMoveType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 转储
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "转储管理")
public class TransportController {

    @Autowired
    private TransportService transportService;

    /**
     * 移动类型列表
     *
     * @out vo 移动类型列表
     */
    @ApiOperation(value = "移动类型列表", tags = {"转储管理"})
    @PostMapping(value = "/transport/getMoveTypeList")
    public BaseResult getMoveTypeList(BizContext ctx) {
        transportService.getMoveTypeList(ctx);
        MultiResultVO<DicMoveType> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 页面初始化
     *
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"转储管理"})
    @PostMapping(value = "/transport/init")
    public BaseResult init(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存 - 只选到物料
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"转储管理"})
    @PostMapping(value = "/transport/getStock")
    public BaseResult getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 转储物料导入
     */
    @ApiOperation(value = "转储物料导入", notes = "转储物料导入", tags = {"转储管理-转储物料导入"})
    @PostMapping(path = "/transport/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> importMaterial(@RequestPart("file") MultipartFile file,@RequestPart("po") String po,BizContext ctx) {
        transportService.importMaterial(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo =ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }
    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表 - 分页", tags = {"转储管理"})
    @PostMapping(value = "/transport/results")
    public BaseResult getPage(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 获取盘点人列表
     *
     * @return 用户列表
     */
    @ApiOperation(value = "获取整理人列表", tags = {"转储管理"})
    @PostMapping(value = "/transport/user-list", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<PageObjectVO<SysUserDTO>> getUserList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getUserList(ctx);
        PageObjectVO<SysUserDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"转储管理"})
    @GetMapping(value = "/transport/{id}")
    public BaseResult getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"转储管理"})
    @PostMapping(value = "/transport/save")
    public BaseResult save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"转储管理"})
    @PostMapping(value = "/transport/submit")
    public BaseResult submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"转储管理"})
    @PostMapping(value = "/transport/post")
    public BaseResult post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"转储管理"})
    @DeleteMapping("/transport/{id}")
    public BaseResult delete(@PathVariable("id") Long id, BizContext ctx) {
        transportService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转储管理"})
    @PostMapping("/transport/write-off")
    public BaseResult writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 获取WBS集合
     */
    @ApiOperation(value = "获取WBS集合", tags = {"转储管理"})
    @PostMapping(value = "/transport/wbs")
    public BaseResult getWbsList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportService.getWbsList(ctx);
        MultiResultVO<ErpWbs> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 导出
     */
    @ApiOperation(value = "导出", tags = {"转储管理"})
    @PostMapping(value = "/transport/export", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<?> export(@RequestBody BizReceiptTransportHeadSearchPO po,
                                BizContext ctx) {
        transportService.export(ctx);
        return  BaseResult.success(EnumReturnMsg.RETURN_CODE_SUCCESS);
    }

    /**
     * 转储物料导入
     */
    @ApiOperation(value = "转储物料导入", notes = "转储物料导入", tags = {"转储管理-物料转储"})
    @PostMapping(path = "/transport/import-item", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResult<MultiResultVO<BizReceiptTransportItemDTO>> importData(@RequestPart("file") MultipartFile file,
                                                                            @RequestParam("outputFtyId") Long outputFtyId,
                                                                            @RequestParam("outputLocationId") Long outputLocationId,
                                                                            @RequestParam("inputFtyId") Long inputFtyId,
                                                                            @RequestParam("inputLocationId") Long inputLocationId,
                                                                            BizContext ctx) {
        transportService.importData(ctx, file, outputFtyId, outputLocationId, inputFtyId, inputLocationId);
        MultiResultVO<BizReceiptTransportItemDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

}