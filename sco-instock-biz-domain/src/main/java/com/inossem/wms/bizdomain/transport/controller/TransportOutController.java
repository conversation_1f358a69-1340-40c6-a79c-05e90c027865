package com.inossem.wms.bizdomain.transport.controller;

import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;

import com.inossem.wms.bizdomain.transport.service.biz.TransportOutService;
import com.inossem.wms.common.constant.Const;
import com.inossem.wms.common.model.bizbasis.dto.BizReceiptAssembleRuleDTO;
import com.inossem.wms.common.model.bizdomain.transport.dto.BizReceiptTransportHeadDTO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportHeadSearchPO;
import com.inossem.wms.common.model.bizdomain.transport.po.BizReceiptTransportWriteOffPO;
import com.inossem.wms.common.model.bizdomain.transport.vo.BizReceiptTransportOutPreHeadVo;
import com.inossem.wms.common.model.common.base.BaseResult;
import com.inossem.wms.common.model.common.base.BizContext;
import com.inossem.wms.common.model.common.base.BizResultVO;
import com.inossem.wms.common.model.common.base.MultiResultVO;
import com.inossem.wms.common.model.common.base.PageObjectVO;
import com.inossem.wms.common.model.common.base.SingleResultVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 调拨出库
 *
 * <AUTHOR>
 */

@RestController
@Api(tags = "调拨管理-调拨出库")
public class TransportOutController {

    @Autowired
    private TransportOutService transportOutService;

    /**
     * 页面初始化
     *
     * @in po 查询条件
     * @out vo 按钮组以及tab页签
     */
    @ApiOperation(value = "页面初始化", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/init")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> init(BizContext ctx) {
        transportOutService.init(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 查询库存
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "查询库存", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/getStock")
    public BaseResult<SingleResultVO<BizReceiptAssembleRuleDTO>>
        getStock(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportOutService.getStock(ctx);
        SingleResultVO<BizReceiptAssembleRuleDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 调拨出库-前置单据调拨申请
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "调拨出库-前置单据调拨申请", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/mat-list")
    public BaseResult<MultiResultVO<BizReceiptTransportOutPreHeadVo>>
        getReferReceiptItemList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportOutService.getReferReceiptItemList(ctx);
        MultiResultVO<BizReceiptTransportOutPreHeadVo> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表分页", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/results")
    public BaseResult<PageObjectVO<BizReceiptTransportHeadDTO>> getPage(@RequestBody BizReceiptTransportHeadSearchPO po,
        BizContext ctx) {
        transportOutService.getPage(ctx);
        PageObjectVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 列表 - 没有分页
     *
     * @in po 查询条件
     * @out vo 列表
     */
    @ApiOperation(value = "列表", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/list")
    public BaseResult<MultiResultVO<BizReceiptTransportHeadDTO>>
        getList(@RequestBody BizReceiptTransportHeadSearchPO po, BizContext ctx) {
        transportOutService.getList(ctx);
        MultiResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 详情
     *
     * @in id 主键
     * @out vo 详情
     */
    @ApiOperation(value = "详情", tags = {"调拨管理-调拨出库"})
    @GetMapping(value = "/transport/out/{id}")
    public BaseResult<BizResultVO<BizReceiptTransportHeadDTO>> getInfo(@PathVariable("id") Long id, BizContext ctx) {
        transportOutService.getInfo(ctx);
        BizResultVO<BizReceiptTransportHeadDTO> vo = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(vo);
    }

    /**
     * 保存
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "保存", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/save")
    public BaseResult<String> save(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportOutService.save(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 提交
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "提交", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/submit")
    public BaseResult<String> submit(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportOutService.submit(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 过账
     *
     * @in po 详情
     * @out code 单据号
     */
    @ApiOperation(value = "过账", tags = {"调拨管理-调拨出库"})
    @PostMapping(value = "/transport/out/post")
    public BaseResult<String> post(@RequestBody BizReceiptTransportHeadDTO po, BizContext ctx) {
        transportOutService.post(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 删除
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "删除", tags = {"调拨管理-调拨出库"})
    @DeleteMapping("/transport/out/{id}")
    public BaseResult<String> delete(@PathVariable("id") Long id, BizContext ctx) {
        transportOutService.delete(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

    /**
     * 冲销
     *
     * @in id 主键
     * @out code 单据号
     */
    @ApiOperation(value = "冲销", tags = {"转储管理"})
    @PostMapping("/transport/out/write-off")
    public BaseResult<String> writeOff(@RequestBody BizReceiptTransportWriteOffPO po, BizContext ctx) {
        transportOutService.writeOff(ctx);
        String code = ctx.getContextData(Const.BIZ_CONTEXT_KEY_VO);
        return BaseResult.success(code);
    }

}