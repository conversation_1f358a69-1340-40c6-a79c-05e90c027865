package com.inossem.wms.common.model.bizdomain.contract.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptContractReceivingHead", description = "合同收货抬头")
@TableName("biz_receipt_contract_receiving_head")
public class BizReceiptContractReceivingHead implements Serializable {
    private static final long serialVersionUID = -5155571055795491721L;


    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "单据号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型，402-其他合同，403-框架合同")
    private Integer receiptType;

    @ApiModelProperty(value = "单据状态")
    private Integer receiptStatus;

    @ApiModelProperty(value = "收货类型，1非生产物资类&服务类&施工类合同收货，2 资产类 ")
    private Integer receiveType;

    @ApiModelProperty(value = "合同Id", notes = "必填,最大长度100")
    private Long contractId;

    @ApiModelProperty(value = "托收PO批次id", notes = "必填,最大长度100")
    private Long deliveryId;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "采购订单号")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "项目经理审批人id")
    private Long pmUserId;

    @ApiModelProperty(value = "专工审批人id")
    private Long assignUserId;

    @ApiModelProperty(value = "经营部审核人id")
    private Long businessUserId;

    @ApiModelProperty(value = "是否存在考勤确认")
    private Integer isAttendance;

    @ApiModelProperty(value = "填报人姓名")
    private String applicantUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间")
    private Date submitTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人ID")
    private Long submitUserId;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;
}
