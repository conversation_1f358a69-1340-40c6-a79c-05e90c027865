package com.inossem.wms.common.model.bizdomain.bi.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BI采购年度预算金额导出视图对象
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BiPurchaseBudgetYearExportVO", description = "BI采购年度预算金额导出视图对象")
public class BiPurchaseBudgetYearExportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份")
    private Integer budgetYear;

    @ApiModelProperty(value = "预算金额(USD)")
    @ExcelProperty(value = "预算金额(USD)")
    private BigDecimal budgetAmount;

}
