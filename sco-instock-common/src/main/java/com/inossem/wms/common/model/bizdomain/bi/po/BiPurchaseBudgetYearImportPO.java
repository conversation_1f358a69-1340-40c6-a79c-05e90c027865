package com.inossem.wms.common.model.bizdomain.bi.po;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BI采购年度预算金额导入入参类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "BiPurchaseBudgetYearImportPO", description = "BI采购年度预算金额导入入参类")
public class BiPurchaseBudgetYearImportPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "年份")
    @ExcelProperty(value = "年份", index = 0)
    private Integer budgetYear;

    @ApiModelProperty(value = "预算金额(USD)")
    @ExcelProperty(value = "预算金额(USD)", index = 1)
    private BigDecimal budgetAmount;

}
