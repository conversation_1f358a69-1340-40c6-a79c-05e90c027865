package com.inossem.wms.common.model.bizdomain.contract.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptContractSubItem", description = "合同分项信息")
@TableName("biz_receipt_contract_sub_item")
public class BizReceiptContractSubItem implements Serializable {
    private static final long serialVersionUID = 7586457490882040883L;


    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "行号")
    private String rid;

    @ApiModelProperty(value = "合同头表ID")
    private Long headId;

    @ApiModelProperty(value = "合同收货id")
    private Long receiveId;

    @ApiModelProperty(value = "合同收货数量")
    private BigDecimal receiveQty;

    @ApiModelProperty(value = "合同收货产值")
    private BigDecimal receiveValue;

    @ApiModelProperty(value = "付款计划id")
    private Long paymentPlanId;

    @ApiModelProperty(value = "付款计划完成数量")
    private BigDecimal paymentPlanQty;

    @ApiModelProperty(value = "付款计划完成产值")
    private BigDecimal paymentPlanValue;

    @ApiModelProperty(value = "累计完成数量")
    private BigDecimal totalQty;

    @ApiModelProperty(value = "累计完成产值")
    private BigDecimal totalValue;

    @ApiModelProperty(value = "项目或费用名称")
    private String subItemName;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "修正不含税单价/框架协议单价")
    private BigDecimal noTaxPriceAmend;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "逻辑删除标识")
    @TableLogic
    private Long isDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "修改人ID")
    private Long modifyUserId;

    @ApiModelProperty(value = "币种")
    @TableField(exist = false)
    private String currencyI18n;


}
