package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * BI采购年度预算金额实体
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("bi_purchase_budget_year")
@ApiModel(value = "BI采购年度预算金额实体", description = "BI采购年度预算金额实体")
public class BiPurchaseBudgetYear implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "预算年份")
    private Integer budgetYear;

    @ApiModelProperty(value = "预算金额(USD)")
    private BigDecimal budgetAmount = BigDecimal.ZERO;

}
