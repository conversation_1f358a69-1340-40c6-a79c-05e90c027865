package com.inossem.wms.common.model.bizdomain.delivery.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 送货通知行项目实体
 * </p>
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2021-05-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="送货通知行项目实体", description="送货通知行项目实体")
@TableName("biz_receipt_delivery_notice_item")
public class BizReceiptDeliveryNoticeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id" , example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "head表主键id" , example = "157490654281729")
    private Long headId;

    @ApiModelProperty(value = "送货通知单行序号" , example = "1")
    private String rid;

    @ApiModelProperty(value = "前续单据head主键" , example = "111")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键" , example = "111")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型" , example = "214")
    private Integer preReceiptType;

    @ApiModelProperty(value = "前序单据数量" , example = "10")
    private BigDecimal preReceiptQty;

    @ApiModelProperty(value = "参考单据行head主键id" , example = "111")
    private Long referReceiptHeadId;

    @ApiModelProperty(value = "参考单据行item主键id" , example = "111")
    private Long referReceiptItemId;

    @ApiModelProperty(value = "参考单据类型" , example = "240")
    private Integer referReceiptType;

    @ApiModelProperty(value = "10草稿、30已作业、50已完成、60已冲销" , example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "仓库号id" , example = "152214349873153")
    private Long whId;

    @ApiModelProperty(value = "工厂id" , example = "145343907954689")
    private Long ftyId;

    @ApiModelProperty(value = "接收库存地点id" , example = "145725436526593")
    private Long locationId;

    @ApiModelProperty(value = "物料id" , example = "60000001")
    private Long matId;

    @ApiModelProperty(value = "订单单位id" , example = "7")
    private Long unitId;

    @ApiModelProperty(value = "生产日期" , example = "2021-05-10")
    private Date productDate;

    @ApiModelProperty(value = "保质期" , example = "100")
    private Integer shelfLine;

    @ApiModelProperty(value = "计划配送日期(SAP)" , example = "2021-05-10")
    private Date deliveryDatePlan;

    @ApiModelProperty(value = "已验收数量" , example = "10")
    private BigDecimal inspectQty;

    @ApiModelProperty(value = "可送货数量" , example = "10")
    private BigDecimal canDeliveryQty;

    @ApiModelProperty(value = "已入库数量" , example = "10")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "数量" , example = "5")
    private BigDecimal qty;

    @ApiModelProperty(value = "验收标识，0-不验收、1-验收" , example = "0")
    private Integer isInspect;

    @ApiModelProperty(value = "行项目备注" , example = "行项目备注")
    private String itemRemark;

    @ApiModelProperty(value = "是否删除【1是，0否】" , example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间" , example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间" , example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id" , example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id" , example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "箱件编号")
    private String caseCode;

    @ApiModelProperty(value = "包装形式")
    private String packageType;

    @ApiModelProperty(value = "箱件尺寸")
    private String caseSize;

    @ApiModelProperty(value = "毛重")
    private String caseWeight;

    @ApiModelProperty(value = "特殊库存标识")
    private String specStock;

    @ApiModelProperty(value = "箱件编号行项目输入")
    private String caseCodeItem;

    @ApiModelProperty(value = "需求计划单号")
    private String demandPlanCode;

    @ApiModelProperty(value = "需求计划行号")
    private String demandPlanRid;

    @ApiModelProperty(value = "需求ren")
    private String demandPerson;

    @ApiModelProperty(value = "需求部门")
    private String demandDept;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同行号")
    private String contractRid;

    @ApiModelProperty(value = "单价")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "发运货值")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "已登记数量")
    private BigDecimal registerQty;

    @ApiModelProperty(value = "采购单号")
    private String purchaseCode;    

    @ApiModelProperty(value = "采购单行号")
    private String purchaseRid;


    @ApiModelProperty(value = "po不含税单价")
    private BigDecimal poNoTaxPrice;

    @ApiModelProperty(value = "po不含税总价")
    private BigDecimal poNoTaxAmount;

    @ApiModelProperty(value = "倍率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "不含税总价")
    private BigDecimal noTaxAmount;

    @ApiModelProperty(value = "税码")
    private Integer taxCode;

    @ApiModelProperty(value = "税码税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "车辆编号")
    private String carCode;

    @ApiModelProperty(value = "司机姓名")
    private String driverName;  

    @ApiModelProperty(value = "联系方式")
    private String contactWay;

    @ApiModelProperty(value = "发票号")
    private String invoiceNo;

    @ApiModelProperty(value = "发票日期")
    private Date invoiceDate;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;


    @ApiModelProperty(value = "能殷单价（不含税）取值=【单价（不含税）/ 汇率 】  *  倍率     当能殷单价（不含税）值≤1时，取1；当能殷单价（不含税）值＞1时，四舍五入取整；不保留小数位")
    private BigDecimal nyNoTaxPrice;

    @ApiModelProperty(value = "能殷总价（不含税）取值=换算后的能殷单价（不含税）*数量")
    private BigDecimal nyNoTaxAmount;

    @ApiModelProperty(value = "内贸倍率")
    private BigDecimal nyTaxRate;


    @ApiModelProperty(value = "物资类别", example = "1")
    private String matTypeStr;

    @ApiModelProperty(value = "费用占比", example = "1")
    private BigDecimal feeRatio;

    @ApiModelProperty(value = "币种")
    private Integer currency;

    @ApiModelProperty(value = "合同税码")
    private Integer contractTaxCode;

    @ApiModelProperty(value = "报关分类")
    private String customsClass;
}
