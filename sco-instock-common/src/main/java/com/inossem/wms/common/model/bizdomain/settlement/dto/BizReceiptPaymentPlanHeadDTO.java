package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import com.inossem.wms.common.annotation.SonAttr;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptOperationLogDTO;
import com.inossem.wms.common.model.bizbasis.dto.BizCommonReceiptRelationDTO;
import com.inossem.wms.common.model.bizdomain.contract.entity.BizReceiptContractSubItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanHeadDTO", description = "付款计划")
public class BizReceiptPaymentPlanHeadDTO implements Serializable {
    private static final long serialVersionUID = 7345514404311445642L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @SonAttr(sonTbName = "biz_common_receipt_operation_log", sonTbFkAttrName = "receiptHeadId")
    @ApiModelProperty(value = "填充属性 -单据日志")
    private List<BizCommonReceiptOperationLogDTO> logList;

    @ApiModelProperty(value = "扩展属性 - 单据流")
    private List<BizCommonReceiptRelationDTO> relationList;


    @SonAttr(sonTbName = "biz_receipt_payment_plan_item", sonTbFkAttrName = "headId")
    @ApiModelProperty(value = "填充属性 - 行项目")
    private List<BizReceiptPaymentPlanItemDTO> itemList;

    @ApiModelProperty(value = "付款计划单号", example = "PD01000216")
    private String receiptCode;

    @RlatAttr(rlatTableName = "biz_receipt_contract_head",
            sourceAttrName = "receiptCode,firstParty,contractName,purchaseType,supplierId,currency,createUserId",
            targetAttrName = "contractCode,firstParty,contractName,purchaseType,supplierId,currency,contractCreateUserId")
    @ApiModelProperty(value = "合同id", example = "PD01000216")
    private Long contractId;

    @ApiModelProperty(value = "合同号")
    private String contractCode;

    @ApiModelProperty(value = "合同号")
    private String contractName;

    @ApiModelProperty(value = "甲方")
    private Integer firstParty;

    @ApiModelProperty(value = "甲方")
    private String firstPartyI18n;

    @ApiModelProperty(value = "合同类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "合同类型")
    private String purchaseTypeI18n;

    @RlatAttr(rlatTableName = "dic_supplier", sourceAttrName = "supplierCode,supplierName", targetAttrName = "supplierCode,supplierName")
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "合同总额含税")
    private BigDecimal contractAmount;

    @ApiModelProperty(value = "付款节点", example = "PD01000216")
    private Integer paymentNode;

    @ApiModelProperty(value = "支付比例")
    private BigDecimal rate;

    @ApiModelProperty(value = "币种", example = "PD01000216")
    private Integer currency;

    @ApiModelProperty(value = "币种")
    private String currencyI18n;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "contractCreateUserCode,contractCreateUserName")
    @ApiModelProperty(value = "合同创建人id")
    private Long contractCreateUserId;

    @ApiModelProperty(value = "合同创建人编码")
    private String contractCreateUserCode;

    @ApiModelProperty(value = "合同创建人描述")
    private String contractCreateUserName;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "清款金额含税）", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "createUserCode,createUserName")
    @ApiModelProperty(value = "创建人id")
    private Long createUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "modifyUserCode,modifyUserName")
    @ApiModelProperty(value = "修改人id")
    private Long modifyUserId;

    @RlatAttr(rlatTableName = "sys_user", sourceAttrName = "userCode,userName", targetAttrName = "submitUserCode,submitUserName")
    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "填充属性 - 创建人编码", example = "Admin")
    private String createUserCode;

    @ApiModelProperty(value = "填充属性 -创建人名称", example = "管理员")
    private String createUserName;

    @ApiModelProperty(value = "填充属性 -修改人编码", example = "Admin")
    private String modifyUserCode;

    @ApiModelProperty(value = "填充属性 -修改人名称", example = "管理员")
    private String modifyUserName;

    @ApiModelProperty(value = "填充属性 -提交人编码", example = "Admin")
    private String submitUserCode;

    @ApiModelProperty(value = "填充属性 -提交人名称", example = "管理员")
    private String submitUserName;

    @ApiModelProperty(value = "扩展属性 - 单据类型名称")
    private String receiptTypeI18n;

    @ApiModelProperty(value = "扩展属性 - 单据状态名称")
    private String receiptStatusI18n;

    @ApiModelProperty(value = "计划类型(1默认为节点类，2定额，3油品，4资产类)")
    private Integer planType;

    private List<BizReceiptContractSubItem> subItemList;

    @ApiModelProperty(value = "资金计划单号", example = "2500")
    private String capitalPlanCode;

    @ApiModelProperty(value = "已支付合同币种金额", example = "2500")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "未支付合同币种金额", example = "2500")
    private BigDecimal notPaidAmount;

    @ApiModelProperty(value = "付款节点", example = "PD01000216")
    private String paymentNodeI18n;

    @ApiModelProperty(value = "合同收货单据id")
    private Long receiveId;

    @ApiModelProperty(value = "工程及服务工作量确认单id")
    private Long receiveConfirmId;

    @ApiModelProperty(value = "付款名称")
    private String paymentName;

    @ApiModelProperty(value = "是否来自于转储单，1是0否")
    private Integer fromTransport;

    @ApiModelProperty(value = "发运批次")
    private String sendBatch;

    public BizReceiptPaymentPlanHeadDTO() {
        this.setPlanType(1);
    }
}
