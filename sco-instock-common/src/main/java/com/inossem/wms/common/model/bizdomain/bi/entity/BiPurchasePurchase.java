package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * BI采购申请表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("bi_purchase_purchase")
@ApiModel(value = "BI采购申请表底表", description = "BI采购申请表实体")
public class BiPurchasePurchase implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "采购申请单号")
    private String receiptCode;

    @ApiModelProperty(value = "单据类型")
    private Integer receiptType;

    @ApiModelProperty(value = "采购计划类型")
    private Integer demandPlanType;

    @ApiModelProperty(value = "采购类型")
    private Integer purchaseType;

    @ApiModelProperty(value = "采购方式")
    private String bidMethod;

    @ApiModelProperty(value = "采购主体")
    private String purchaseSubject;

    @ApiModelProperty(value = "预算出处")
    private Long annualBudgetId;

    @ApiModelProperty(value = "申请预算金额")
    private BigDecimal budgetAmount;

    @ApiModelProperty(value = "采购申请创建时间")
    private Date createTime;

    @ApiModelProperty(value = "采购申请创建人")
    private Long createUserId;

    @ApiModelProperty(value = "采购申请完成时间")
    private Date modifyTime;

    @ApiModelProperty(value = "需求计划单据ID")
    private Long demandHeadId;

}
