package com.inossem.wms.common.model.bizdomain.bi.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BI 查询对象
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class BiSearchPO {

    @ApiModelProperty(value = "公司代码")
    private String bukrs;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "是否累计")
    private Boolean isCumulative = false;

}
