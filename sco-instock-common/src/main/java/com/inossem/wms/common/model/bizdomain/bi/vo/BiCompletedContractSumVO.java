package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "已完成合同数量/金额查询传输对象", description = "已完成合同数量/金额查询传输对象")
public class BiCompletedContractSumVO {

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "已完成采购任务的合同数量")
    private Long totalCount;

    @ApiModelProperty(value = "已完成采购任务的合同金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "完成招投标次数")
    private Long purchaseCount;

}
