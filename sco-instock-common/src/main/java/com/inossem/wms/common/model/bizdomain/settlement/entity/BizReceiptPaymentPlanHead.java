package com.inossem.wms.common.model.bizdomain.settlement.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanHead", description = "付款计划抬头表")
@TableName("biz_receipt_payment_plan_head")
public class BizReceiptPaymentPlanHead implements Serializable {
    private static final long serialVersionUID = -6797843383792892845L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty(value = "付款计划单号", example = "PD01000216")
    private String receiptCode;

    @ApiModelProperty(value = "合同id", example = "PD01000216")
    private Long contractId;

    @ApiModelProperty(value = "付款节点", example = "PD01000216")
    private Integer paymentNode;

    @ApiModelProperty(value = "计划付款月份", example = "2500")
    private String paymentMonth;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxCodeRate;

    @ApiModelProperty(value = "清款金额含税）", example = "2500")
    private BigDecimal qty;

    @ApiModelProperty(value = "单据类型", example = "211", required = false)
    private Integer receiptType;

    @ApiModelProperty(value = "盘点表状态:10-草稿,20-已提交,50-已计数,90-已完成,4-待审批,5-审批通过,6-审批未通过,7-已过账", example = "10")
    private Integer receiptStatus;

    @ApiModelProperty(value = "备注", example = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "提交时间", example = "2021-05-01", required = false)
    private Date submitTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "提交人id", example = "1", required = false)
    private Long submitUserId;

    @ApiModelProperty(value = "支付比例")
    private BigDecimal rate;

    @ApiModelProperty(value = "合同总额含税")
    private BigDecimal contractAmount;


    @ApiModelProperty(value = "计划类型")
    private Integer planType;


    @ApiModelProperty(value = "资金计划单号", example = "2500")
    private String capitalPlanCode;

    @ApiModelProperty(value = "已支付合同币种金额", example = "2500")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "未支付合同币种金额", example = "2500")
    private BigDecimal notPaidAmount;

    @ApiModelProperty(value = "合同收货单据id")
    private Long receiveId;

    @ApiModelProperty(value = "工程及服务工作量确认单id")
    private Long receiveConfirmId;

    @ApiModelProperty(value = "付款名称")
    private String paymentName;

    @ApiModelProperty(value = "是否来自于转储单，1是0否")
    private Integer fromTransport;

}
