package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "预算达成率总和对象", description = "预算达成率总和对象")
public class BiBudgetSumVO {

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "预算金额(USD)")
    private BigDecimal budgetAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已有合同金额(USD)")
    private BigDecimal contractAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "年度预算达成率")
    private BigDecimal amountRate = BigDecimal.ZERO;

}
