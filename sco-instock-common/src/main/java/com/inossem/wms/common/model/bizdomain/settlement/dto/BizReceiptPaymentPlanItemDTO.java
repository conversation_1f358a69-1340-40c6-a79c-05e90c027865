package com.inossem.wms.common.model.bizdomain.settlement.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.inossem.wms.common.annotation.RlatAttr;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "BizReceiptPaymentPlanItemDTO", description = "付款计划行项目")
public class BizReceiptPaymentPlanItemDTO implements Serializable {

    private static final long serialVersionUID = 5942562946400682894L;

    @ApiModelProperty(value = "主键id", example = "159843409264782", required = false)
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @RlatAttr(rlatTableName = "biz_receipt_payment_plan_head", sourceAttrName = "receiveId,receiveConfirmId", targetAttrName = "receiveId,receiveConfirmId")
    @ApiModelProperty(value = "head表主键id", example = "157490654281729")
    private Long headId;

    @RlatAttr(rlatTableName = "biz_receipt_contract_receiving_head", sourceAttrName = "receiptCode", targetAttrName = "receiveReceiptCode")
    private Long receiveId;

    private String receiveReceiptCode;

    @RlatAttr(rlatTableName = "biz_receipt_contract_receiving_head", sourceAttrName = "receiptCode", targetAttrName = "receiveConfirmReceiptCode")
    private Long receiveConfirmId;

    private String receiveConfirmReceiptCode;

    @ApiModelProperty(value = "行序号", example = "1")
    private String rid;

    @ApiModelProperty(value = "单据状态", example = "10")
    private Integer itemStatus;

    @ApiModelProperty(value = "行项目备注", example = "行项目备注")
    private String itemRemark;

    @RlatAttr(rlatTableName = "dic_material", sourceAttrName = "matCode,matName,matNameEn", targetAttrName = "matCode,matName,matNameEn")
    @ApiModelProperty(value = "物料id", example = "1")
    private Long matId;

    @ApiModelProperty(value = "物料编码")
    private String matCode;

    @ApiModelProperty(value = "物料名称")
    private String matName;

    @ApiModelProperty(value = "物料英文名")
    private String matNameEn;

    @ApiModelProperty(value = "品名")
    private String productName;

    @RlatAttr(rlatTableName = "dic_material_group", sourceAttrName = "matGroupCode,matGroupName", targetAttrName = "matGroupCode,matGroupName")
    @ApiModelProperty(value = "物料组ID", notes = "必填,关联物料组主数据")
    private Long matGroupId;

    @ApiModelProperty(value = "物料组编码")
    private String matGroupCode;

    @ApiModelProperty(value = "物料组名称")
    private String matGroupName;

    @RlatAttr(rlatTableName = "dic_unit", sourceAttrName = "unitCode,unitName", targetAttrName = "unitCode,unitName")
    @ApiModelProperty(value = "单位id")
    private Long unitId;

    @ApiModelProperty(value = "单位code")
    private String unitCode;

    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @RlatAttr(rlatTableName = "dic_wbs", sourceAttrName = "wbsName", targetAttrName = "wbsName")
    @ApiModelProperty(value = "wbsId")
    private Long wbsId;

    @ApiModelProperty(value = "wbs名称")
    private String wbsName;

    @ApiModelProperty(value = "成本中心")
    private String costCenter;

    @ApiModelProperty(value = "采购订单号", example = "1")
    private String purchaseReceiptCode;

    @ApiModelProperty(value = "采购订单行号", example = "1")
    private String purchaseReceiptRid;

    @ApiModelProperty(value = "质检会签单id", example = "1")
    private Long signInspectReceiptId;

    @ApiModelProperty(value = "质检会签单id", example = "1")
    private Integer signInspectReceiptType;

    @ApiModelProperty(value = "入库单号id", example = "1")
    private Long inputReceiptId;

    @ApiModelProperty(value = "入库单类型", example = "1")
    private Integer inputReceiptType;

    @ApiModelProperty(value = "质检会签单号", example = "1")
    private String signInspectReceiptCode;

    @ApiModelProperty(value = "入库单号", example = "1")
    private String inputReceiptCode;

    @ApiModelProperty(value = "本次送货数量", example = "1")
    private BigDecimal qty;

    @ApiModelProperty(value = "合格数量", example = "1")
    private BigDecimal qualifiedQty;

    @ApiModelProperty(value = "不合格数量", example = "1")
    private BigDecimal unqualifiedQty;

    @ApiModelProperty(value = "未到货数量", example = "1")
    private BigDecimal unarrivalQty;

    @ApiModelProperty(value = "入库数量", example = "1")
    private BigDecimal inputQty;

    @ApiModelProperty(value = "合同数量", example = "1")
    private BigDecimal contractQty;

    @ApiModelProperty(value = "合同未清数量", example = "1")
    private BigDecimal unContractQty;

    @ApiModelProperty(value = "物料凭证号", example = "*********")
    private String matDocCode;

    @ApiModelProperty(value = "物料凭证行项目号", example = "0010")
    private String matDocRid;

    @ApiModelProperty(value = "物料凭证年度", example = "0010")
    private String matDocYear;

    @ApiModelProperty(value = "不含税单价", example = "1")
    private BigDecimal noTaxPrice;

    @ApiModelProperty(value = "含税单价", example = "1")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "含税总价", example = "1")
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "前续单据head主键")
    private Long preReceiptHeadId;

    @ApiModelProperty(value = "前续单据item主键")
    private Long preReceiptItemId;

    @ApiModelProperty(value = "前续单据类型")
    private Integer preReceiptType;

    @ApiModelProperty(value = "是否删除【1是，0否】", example = "0", required = false)
    @TableLogic
    private Integer isDelete;

    @ApiModelProperty(value = "创建时间", example = "2021-05-01", required = false)
    private Date createTime;

    @ApiModelProperty(value = "修改时间", example = "2021-05-01", required = false)
    private Date modifyTime;

    @ApiModelProperty(value = "创建人id", example = "1", required = false)
    private Long createUserId;

    @ApiModelProperty(value = "修改人id", example = "1", required = false)
    private Long modifyUserId;

    @ApiModelProperty(value = "单据行项目状态名称", example = "草稿")
    private String itemStatusI18n;

    @ApiModelProperty(value = "资产卡片号", example = "ASSET001")
    private String assetCardNo;

    @ApiModelProperty(value = "资产卡片描述", example = "办公设备-打印机")
    private String assetCardDesc;

    @ApiModelProperty(value = "已预制数量")
    private BigDecimal precastQty;

    @ApiModelProperty(value = "已预制金额")
    private BigDecimal precastAmount;
}
