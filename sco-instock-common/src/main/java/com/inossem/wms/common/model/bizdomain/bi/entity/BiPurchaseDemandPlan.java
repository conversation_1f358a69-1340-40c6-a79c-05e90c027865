package com.inossem.wms.common.model.bizdomain.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * BI采购需求计划表实体类
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
@TableName("bi_purchase_demand_plan")
@ApiModel(value = "BI采购需求计划表底表", description = "BI采购需求计划表实体")
public class BiPurchaseDemandPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "需求计划单号")
    private String receiptCode;

    @ApiModelProperty(value = "需求计划创建时间")
    private Date createTime;

    @ApiModelProperty(value = "创建人ID")
    private Long createUserId;

    @ApiModelProperty(value = "处理人ID")
    private Long handleUserId;

    @ApiModelProperty(value = "需求计划完成时间")
    private Date modifyTime;

    @ApiModelProperty(value = "计划类型")
    private Integer demandPlanType;

    @ApiModelProperty(value = "需求类型")
    private Integer demandType;

}
