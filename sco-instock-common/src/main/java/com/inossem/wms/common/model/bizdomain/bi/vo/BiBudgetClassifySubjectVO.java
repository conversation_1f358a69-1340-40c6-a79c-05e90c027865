package com.inossem.wms.common.model.bizdomain.bi.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "预算达成率分类对象", description = "预算达成率分类对象")
public class BiBudgetClassifySubjectVO {

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "预算编码")
    private String budgetCode;

    @ApiModelProperty(value = "预算名称")
    private String budgetName;

    @ApiModelProperty(value = "预算达成率")
    private BigDecimal amountRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "预算金额(USD)")
    private BigDecimal budgetAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "已有合同金额(USD)")
    private BigDecimal contractAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "创建人编码")
    private String createUserCode;

    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
